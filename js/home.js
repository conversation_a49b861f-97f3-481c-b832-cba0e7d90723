// JavaScript para la página de inicio de Ecos del Alma

class HomePage {
    constructor() {
        this.blogs = [];
        this.filteredBlogs = [];
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.currentModalIndex = -1;
        
        this.init();
    }

    init() {
        this.loadBlogs();
        this.setupEventListeners();
        this.renderBlogs();
        this.updateResultsCount();
        this.setupScrollAnimations();
        this.recordVisit();
    }

    loadBlogs() {
        // Cargar solo blogs publicados y destacados para la página principal
        this.blogs = EcosDB.getBlogs()
            .filter(blog => blog.published)
            .slice(0, 6); // Mostrar solo los primeros 6 blogs
        this.filteredBlogs = [...this.blogs];
    }

    setupEventListeners() {
        // Búsqueda
        const searchInput = $('#searchInput');
        const clearSearch = $('#clearSearch');
        
        if (searchInput) {
            searchInput.addEventListener('input', EcosUtils.debounce(() => {
                this.searchQuery = searchInput.value.trim();
                this.applyFilters();
                if (clearSearch) {
                    clearSearch.classList.toggle('hidden', !this.searchQuery);
                }
            }, 300));
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                if (searchInput) {
                    searchInput.value = '';
                    this.searchQuery = '';
                    this.applyFilters();
                    clearSearch.classList.add('hidden');
                }
            });
        }

        // Filtros de categoría
        $$('#filters .filter-chip').forEach(btn => {
            btn.addEventListener('click', () => {
                $$('#filters .filter-chip').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentFilter = btn.dataset.filter;
                this.applyFilters();
            });
        });

        // Botón "Sorpréndeme"
        const randomRead = $('#randomRead');
        if (randomRead) {
            randomRead.addEventListener('click', () => {
                this.openRandomBlog();
            });
        }

        // Botón "Load more"
        const loadMore = $('#loadMore');
        if (loadMore) {
            loadMore.addEventListener('click', () => {
                // Redirigir a la página de blogs completa
                window.location.href = 'blogs.html';
            });
        }

        // Suscripción
        const subscribeForm = $('#subscribeForm');
        if (subscribeForm) {
            subscribeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSubscribe(e);
            });
        }

        // Contacto
        const contactForm = $('#contactForm');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleContact(e);
            });
        }

        // Modal de navegación
        const modalPrev = $('#modalPrev');
        const modalNext = $('#modalNext');
        
        if (modalPrev) {
            modalPrev.addEventListener('click', () => this.navigateBlog(-1));
        }
        
        if (modalNext) {
            modalNext.addEventListener('click', () => this.navigateBlog(1));
        }

        // Cerrar modal con ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
            }
        });

        // Mobile menu
        window.openMobileMenu = (e) => {
            e.preventDefault();
            $('#mobileMenu').classList.toggle('hidden');
        };
    }

    applyFilters() {
        let filtered = [...this.blogs];

        // Aplicar filtro de búsqueda
        if (this.searchQuery) {
            const query = EcosUtils.normalizeText(this.searchQuery);
            filtered = filtered.filter(blog => {
                const title = EcosUtils.normalizeText(blog.title);
                const content = EcosUtils.normalizeText(blog.content);
                const tags = blog.tags ? blog.tags.map(tag => EcosUtils.normalizeText(tag)).join(' ') : '';
                
                return title.includes(query) || 
                       content.includes(query) || 
                       tags.includes(query);
            });
        }

        // Aplicar filtro de categoría
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(blog => {
                // Mapear filtros a categorías
                const categoryMap = {
                    'poesia': 'poesia',
                    'reflexiones': 'reflexiones',
                    'naturaleza': 'naturaleza',
                    'amor': 'amor'
                };
                
                const targetCategory = categoryMap[this.currentFilter];
                return blog.category === targetCategory || 
                       (blog.tags && blog.tags.some(tag => 
                           EcosUtils.normalizeText(tag).includes(EcosUtils.normalizeText(this.currentFilter))
                       ));
            });
        }

        this.filteredBlogs = filtered;
        this.renderBlogs();
        this.updateResultsCount();
    }

    renderBlogs() {
        const postsGrid = $('#postsGrid');
        if (!postsGrid) return;

        // Limpiar contenido existente pero mantener estructura
        const existingCards = $$('.post-card', postsGrid);
        existingCards.forEach(card => {
            if (!card.classList.contains('hidden-post')) {
                card.style.display = 'none';
            }
        });

        // Mostrar blogs filtrados
        this.filteredBlogs.forEach((blog, index) => {
            if (index < 3) { // Solo mostrar los primeros 3 en la vista principal
                const existingCard = existingCards[index];
                if (existingCard) {
                    this.updateBlogCard(existingCard, blog);
                    existingCard.style.display = 'flex';
                }
            }
        });

        // Ocultar cards no utilizadas
        for (let i = this.filteredBlogs.length; i < 3; i++) {
            if (existingCards[i]) {
                existingCards[i].style.display = 'none';
            }
        }
    }

    updateBlogCard(cardElement, blog) {
        // Actualizar título
        const titleElement = cardElement.querySelector('h3');
        if (titleElement) {
            titleElement.textContent = blog.title;
        }

        // Actualizar excerpt
        const excerptElement = cardElement.querySelector('p');
        if (excerptElement) {
            excerptElement.textContent = blog.excerpt || this.extractExcerpt(blog.content);
        }

        // Actualizar tags
        const tagsContainer = cardElement.querySelector('.flex.flex-wrap.gap-2');
        if (tagsContainer && blog.tags) {
            tagsContainer.innerHTML = blog.tags.slice(0, 2).map(tag => 
                `<span class="glass text-xs px-3 py-1 rounded-full">${EcosUtils.escapeHtml(tag)}</span>`
            ).join('');
        }

        // Actualizar botón de lectura
        const readButton = cardElement.querySelector('.btn-read');
        if (readButton) {
            readButton.setAttribute('data-open', blog.id);
            readButton.addEventListener('click', () => this.openBlogModal(blog.id));
        }

        // Actualizar data attributes
        cardElement.setAttribute('data-id', blog.id);
        cardElement.setAttribute('data-tags', blog.tags ? blog.tags.join(' ') : '');
        cardElement.setAttribute('data-title', blog.title);
    }

    extractExcerpt(content) {
        const div = document.createElement('div');
        div.innerHTML = content;
        const text = div.textContent || div.innerText || '';
        return EcosUtils.truncateText(text, 100);
    }

    updateResultsCount() {
        const resultsCount = $('#resultsCount');
        if (resultsCount) {
            const total = this.filteredBlogs.length;
            if (this.searchQuery || this.currentFilter !== 'all') {
                resultsCount.textContent = total === this.blogs.length ? '' : `${total} encontrados`;
            } else {
                resultsCount.textContent = '';
            }
        }
    }

    openRandomBlog() {
        if (this.filteredBlogs.length === 0) {
            EcosUtils.toast('No hay blogs disponibles');
            return;
        }
        
        const randomIndex = Math.floor(Math.random() * this.filteredBlogs.length);
        const randomBlog = this.filteredBlogs[randomIndex];
        this.openBlogModal(randomBlog.id);
    }

    openBlogModal(blogId) {
        const blog = this.blogs.find(b => b.id === blogId);
        if (!blog) return;

        // Registrar vista
        EcosDB.recordBlogView(blogId);
        
        // Encontrar índice en la lista filtrada
        this.currentModalIndex = this.filteredBlogs.findIndex(b => b.id === blogId);

        // Llenar modal
        const modalTitle = $('#modalTitle');
        const modalBody = $('#modalBody');

        if (modalTitle) modalTitle.textContent = blog.title;
        if (modalBody) modalBody.innerHTML = blog.content;

        // Mostrar modal
        const modal = $('#postModal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal() {
        const modal = $('#postModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }

    navigateBlog(direction) {
        if (this.currentModalIndex === -1) return;

        const newIndex = this.currentModalIndex + direction;
        if (newIndex >= 0 && newIndex < this.filteredBlogs.length) {
            const nextBlog = this.filteredBlogs[newIndex];
            this.openBlogModal(nextBlog.id);
        }
    }

    handleSubscribe(event) {
        const form = event.target;
        const formData = new FormData(form);
        
        const name = formData.get('name')?.trim() || '';
        const email = formData.get('email')?.trim();

        if (!email) {
            EcosUtils.toast('Por favor ingresa tu email', 'error');
            return;
        }

        if (!EcosUtils.isValidEmail(email)) {
            EcosUtils.toast('Por favor ingresa un email válido', 'error');
            return;
        }

        // Verificar si ya está suscrito
        const subscribers = EcosDB.getSubscribers();
        const existingSubscriber = subscribers.find(sub => sub.email === email);
        
        if (existingSubscriber) {
            EcosUtils.toast('Ya estás suscrito con este email', 'warning');
            return;
        }

        // Agregar suscriptor
        const subscriber = {
            name: name || 'Suscriptor',
            email: email
        };

        EcosDB.addSubscriber(subscriber);
        EcosUtils.toast(`¡Bienvenido${name ? ', ' + name : ''}! Te has suscrito exitosamente`);
        
        // Limpiar formulario
        form.reset();
    }

    handleContact(event) {
        const form = event.target;
        const formData = new FormData(form);
        
        const name = formData.get('name')?.trim();
        const email = formData.get('email')?.trim();
        const message = formData.get('message')?.trim();

        if (!name || !email || !message) {
            EcosUtils.toast('Por favor completa todos los campos', 'error');
            return;
        }

        if (!EcosUtils.isValidEmail(email)) {
            EcosUtils.toast('Por favor ingresa un email válido', 'error');
            return;
        }

        // Agregar mensaje
        const contactMessage = {
            name: name,
            email: email,
            message: message
        };

        EcosDB.addMessage(contactMessage);
        EcosUtils.toast('¡Mensaje enviado! Te responderemos pronto');
        
        // Mostrar preview del mensaje
        const preview = document.createElement('div');
        preview.className = 'mt-3 glass rounded-xl p-4 text-white/90';
        preview.innerHTML = `
            <div class="text-sm">De: <strong>${EcosUtils.escapeHtml(name)}</strong> &lt;${EcosUtils.escapeHtml(email)}&gt;</div>
            <div class="mt-2 whitespace-pre-wrap">${EcosUtils.escapeHtml(message)}</div>
        `;
        form.parentNode.appendChild(preview);
        
        // Limpiar formulario
        form.reset();

        // Remover preview después de 10 segundos
        setTimeout(() => {
            if (preview.parentNode) {
                preview.parentNode.removeChild(preview);
            }
        }, 10000);
    }

    setupScrollAnimations() {
        // Configurar Intersection Observer para animaciones de scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, observerOptions);

        // Observar elementos que deben animarse
        const elementsToAnimate = document.querySelectorAll('.scroll-reveal');
        elementsToAnimate.forEach(el => {
            observer.observe(el);
        });
    }

    recordVisit() {
        // Registrar visita del usuario
        if (window.EcosDB) {
            // Intentar obtener país del usuario (simplificado)
            const country = this.getUserCountry();
            EcosDB.recordVisit(country);
        }
    }

    getUserCountry() {
        // Función simplificada para obtener país
        // En un entorno real, usarías una API de geolocalización
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const countryMap = {
            'America/Mexico_City': 'México',
            'America/New_York': 'Estados Unidos',
            'Europe/Madrid': 'España',
            'America/Argentina/Buenos_Aires': 'Argentina',
            'America/Bogota': 'Colombia',
            'America/Lima': 'Perú',
            'America/Santiago': 'Chile'
        };

        return countryMap[timezone] || 'Desconocido';
    }
}

// Funciones globales para compatibilidad
window.closeModal = () => {
    if (window.homePage) {
        homePage.closeModal();
    }
};

window.handleSubscribe = (event) => {
    if (window.homePage) {
        homePage.handleSubscribe(event);
    }
};

window.handleContact = (event) => {
    if (window.homePage) {
        homePage.handleContact(event);
    }
};

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.homePage = new HomePage();
});
