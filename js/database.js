// Sistema de Base de Datos Local para Ecos del Alma
class EcosDatabase {
    constructor() {
        this.initializeDatabase();
    }

    // Inicializar la base de datos con datos por defecto
    initializeDatabase() {
        if (!localStorage.getItem('ecos_blogs')) {
            this.initializeBlogs();
        }
        if (!localStorage.getItem('ecos_subscribers')) {
            localStorage.setItem('ecos_subscribers', JSON.stringify([]));
        }
        if (!localStorage.getItem('ecos_messages')) {
            localStorage.setItem('ecos_messages', JSON.stringify([]));
        }
        if (!localStorage.getItem('ecos_analytics')) {
            this.initializeAnalytics();
        }
        if (!localStorage.getItem('ecos_settings')) {
            this.initializeSettings();
        }
    }

    // Inicializar blogs con contenido en español
    initializeBlogs() {
        const defaultBlogs = [
            {
                id: 'b1',
                title: 'Un Florecer Silencioso',
                excerpt: 'En el silencio entre pensamientos, algo tierno se despliega...',
                content: `<p><PERSON>gunas ma<PERSON>nas, el silencio habla antes que yo. Se despliega como un pétalo, sin prisa, sin miedo, simplemente abriéndose. En esa quietud, me encuentro conmigo misma y lo llamo amor.</p><p>Y si el mundo insiste en el trueno, mantendré un cuarto de luz donde los susurros puedan crecer salvajes.</p>`,
                category: 'poesia',
                tags: ['poesía', 'amor', 'silencio'],
                date: new Date('2024-01-15').toISOString(),
                views: 245,
                featured: true,
                published: true
            },
            {
                id: 'b2',
                title: 'Guijarros y Ríos',
                excerpt: 'Cada pequeña elección remodela la corriente, y la orilla recuerda...',
                content: `<p>Llevo guijarros de momentos en mi bolsillo. Cada uno, suave con el tiempo, me recuerda que el río es paciente, y yo también puedo serlo.</p><p>Fluir no es olvidar — es recordar en movimiento.</p>`,
                category: 'reflexiones',
                tags: ['reflexiones', 'naturaleza', 'tiempo'],
                date: new Date('2024-01-10').toISOString(),
                views: 189,
                featured: false,
                published: true
            },
            {
                id: 'b3',
                title: 'Cartas al Cielo',
                excerpt: 'Las nubes guardan los archivos más suaves — nuestros sueños doblados en luz...',
                content: `<p>Algunas cartas que nunca envío, se las doy al cielo. El viento lee todo con bondad.</p><p>Hoy, mi oración fue solo un respiro. Regresó como paz.</p>`,
                category: 'poesia',
                tags: ['poesía', 'naturaleza', 'espiritualidad'],
                date: new Date('2024-01-05').toISOString(),
                views: 312,
                featured: true,
                published: true
            },
            {
                id: 'b4',
                title: 'Cuartos de Luz',
                excerpt: 'Todos guardamos una ventana dentro de nosotros. Algunas tardes, el sol la encuentra...',
                content: `<p>Algunos días, todo lo que necesito es un parche cálido en el suelo para recordar: estoy aquí, y la luz lo sabe.</p>`,
                category: 'reflexiones',
                tags: ['reflexiones', 'amor', 'hogar'],
                date: new Date('2024-01-01').toISOString(),
                views: 156,
                featured: false,
                published: true
            },
            {
                id: 'b5',
                title: 'Tinta y Quietud',
                excerpt: 'Hay un silencio que mi pluma entiende, un espacio por el que nunca se apresura...',
                content: `<p>Entre palabras, un prado. Lo camino lentamente, para que las comas puedan recuperar el aliento.</p>`,
                category: 'poesia',
                tags: ['poesía', 'escritura'],
                date: new Date('2023-12-28').toISOString(),
                views: 203,
                featured: false,
                published: true
            },
            {
                id: 'b6',
                title: 'Lecciones del Amanecer',
                excerpt: 'La mañana enseña suavemente — primero el contorno, luego el mundo...',
                content: `<p>Al primer rayo de luz, hasta mis preocupaciones bostezan. El día es nuevo, y eso es suficiente milagro para comenzar de nuevo.</p>`,
                category: 'reflexiones',
                tags: ['reflexiones', 'naturaleza', 'esperanza'],
                date: new Date('2023-12-25').toISOString(),
                views: 278,
                featured: false,
                published: true
            }
        ];
        localStorage.setItem('ecos_blogs', JSON.stringify(defaultBlogs));
    }

    // Inicializar analytics
    initializeAnalytics() {
        const analytics = {
            totalVisits: 1247,
            uniqueVisitors: 892,
            countries: {
                'España': 342,
                'México': 298,
                'Argentina': 187,
                'Colombia': 156,
                'Chile': 89,
                'Perú': 67,
                'Venezuela': 45,
                'Uruguay': 32,
                'Ecuador': 31
            },
            dailyVisits: this.generateDailyVisits(),
            blogViews: {}
        };
        localStorage.setItem('ecos_analytics', JSON.stringify(analytics));
    }

    // Generar visitas diarias para los últimos 30 días
    generateDailyVisits() {
        const visits = {};
        const today = new Date();
        for (let i = 29; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            visits[dateStr] = Math.floor(Math.random() * 50) + 10;
        }
        return visits;
    }

    // Inicializar configuraciones
    initializeSettings() {
        const settings = {
            siteName: 'Ecos del Alma',
            siteDescription: 'Susurros que se vuelven palabras — un rincón acogedor para poemas, reflexiones e historias gentiles.',
            authorName: 'Alma Escritora',
            authorBio: 'Escribo para trazar los lugares silenciosos que llevamos — los bordes tiernos, los centros suaves, los pequeños cuartos de luz.',
            contactEmail: '<EMAIL>',
            socialLinks: {
                instagram: '',
                twitter: '',
                facebook: ''
            },
            publishingSchedule: {
                poetry: 'Cada dos semanas',
                reflections: 'Mensualmente',
                stories: 'Cuando la luna insiste'
            }
        };
        localStorage.setItem('ecos_settings', JSON.stringify(settings));
    }

    // Métodos para blogs
    getBlogs() {
        return JSON.parse(localStorage.getItem('ecos_blogs') || '[]');
    }

    getBlog(id) {
        const blogs = this.getBlogs();
        return blogs.find(blog => blog.id === id);
    }

    saveBlog(blog) {
        const blogs = this.getBlogs();
        const existingIndex = blogs.findIndex(b => b.id === blog.id);
        
        if (existingIndex >= 0) {
            blogs[existingIndex] = blog;
        } else {
            blog.id = 'b' + Date.now();
            blog.date = new Date().toISOString();
            blog.views = 0;
            blogs.unshift(blog);
        }
        
        localStorage.setItem('ecos_blogs', JSON.stringify(blogs));
        return blog;
    }

    deleteBlog(id) {
        const blogs = this.getBlogs();
        const filtered = blogs.filter(blog => blog.id !== id);
        localStorage.setItem('ecos_blogs', JSON.stringify(filtered));
    }

    // Métodos para suscriptores
    getSubscribers() {
        return JSON.parse(localStorage.getItem('ecos_subscribers') || '[]');
    }

    addSubscriber(subscriber) {
        const subscribers = this.getSubscribers();
        subscriber.id = 's' + Date.now();
        subscriber.date = new Date().toISOString();
        subscribers.unshift(subscriber);
        localStorage.setItem('ecos_subscribers', JSON.stringify(subscribers));
        return subscriber;
    }

    deleteSubscriber(id) {
        const subscribers = this.getSubscribers();
        const filtered = subscribers.filter(sub => sub.id !== id);
        localStorage.setItem('ecos_subscribers', JSON.stringify(filtered));
    }

    // Métodos para mensajes
    getMessages() {
        return JSON.parse(localStorage.getItem('ecos_messages') || '[]');
    }

    addMessage(message) {
        const messages = this.getMessages();
        message.id = 'm' + Date.now();
        message.date = new Date().toISOString();
        message.read = false;
        messages.unshift(message);
        localStorage.setItem('ecos_messages', JSON.stringify(messages));
        return message;
    }

    markMessageAsRead(id) {
        const messages = this.getMessages();
        const message = messages.find(m => m.id === id);
        if (message) {
            message.read = true;
            localStorage.setItem('ecos_messages', JSON.stringify(messages));
        }
    }

    deleteMessage(id) {
        const messages = this.getMessages();
        const filtered = messages.filter(msg => msg.id !== id);
        localStorage.setItem('ecos_messages', JSON.stringify(filtered));
    }

    // Métodos para analytics
    getAnalytics() {
        return JSON.parse(localStorage.getItem('ecos_analytics') || '{}');
    }

    recordVisit(country = 'Desconocido') {
        const analytics = this.getAnalytics();
        analytics.totalVisits = (analytics.totalVisits || 0) + 1;
        
        const today = new Date().toISOString().split('T')[0];
        if (!analytics.dailyVisits) analytics.dailyVisits = {};
        analytics.dailyVisits[today] = (analytics.dailyVisits[today] || 0) + 1;
        
        if (!analytics.countries) analytics.countries = {};
        analytics.countries[country] = (analytics.countries[country] || 0) + 1;
        
        localStorage.setItem('ecos_analytics', JSON.stringify(analytics));
    }

    // Registrar login de admin
    recordAdminLogin() {
        const analytics = this.getAnalytics();
        const today = new Date().toISOString().split('T')[0];

        if (!analytics.adminLogins) {
            analytics.adminLogins = {};
        }

        if (!analytics.adminLogins[today]) {
            analytics.adminLogins[today] = 0;
        }
        analytics.adminLogins[today]++;

        localStorage.setItem('ecos_analytics', JSON.stringify(analytics));
    }

    recordBlogView(blogId) {
        const analytics = this.getAnalytics();
        if (!analytics.blogViews) analytics.blogViews = {};
        analytics.blogViews[blogId] = (analytics.blogViews[blogId] || 0) + 1;
        
        // También actualizar las vistas en el blog
        const blogs = this.getBlogs();
        const blog = blogs.find(b => b.id === blogId);
        if (blog) {
            blog.views = (blog.views || 0) + 1;
            localStorage.setItem('ecos_blogs', JSON.stringify(blogs));
        }
        
        localStorage.setItem('ecos_analytics', JSON.stringify(analytics));
    }

    // Métodos para configuraciones
    getSettings() {
        return JSON.parse(localStorage.getItem('ecos_settings') || '{}');
    }

    saveSettings(settings) {
        localStorage.setItem('ecos_settings', JSON.stringify(settings));
    }
}

// Instancia global de la base de datos
window.EcosDB = new EcosDatabase();
