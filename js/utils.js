// Utilidades para Ecos del Alma

// Selectores DOM simplificados
const $ = (sel, root = document) => root.querySelector(sel);
const $$ = (sel, root = document) => Array.from(root.querySelectorAll(sel));

// Sistema de notificaciones toast
const toast = (msg, type = 'success') => {
    const toastEl = $('#toast');
    const toastMsg = $('#toastMsg');
    
    if (!toastEl || !toastMsg) return;
    
    toastMsg.textContent = msg;
    toastEl.className = `fixed bottom-6 left-1/2 -translate-x-1/2 z-50`;
    
    // Aplicar estilos según el tipo
    const toastContent = toastEl.querySelector('div');
    if (toastContent) {
        toastContent.className = `glass rounded-xl px-4 py-3 flex items-center gap-2 ${
            type === 'error' ? 'bg-red-500/25' : 
            type === 'warning' ? 'bg-yellow-500/25' : 
            'bg-softpink/25'
        }`;
    }
    
    clearTimeout(window.__toastTimer);
    window.__toastTimer = setTimeout(() => {
        toastEl.className += ' hidden';
    }, 3000);
};

// Formatear fechas en español
const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        timeZone: 'UTC'
    };
    return date.toLocaleDateString('es-ES', options);
};

// Formatear fechas relativas
const formatRelativeDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Ayer';
    if (diffDays < 7) return `Hace ${diffDays} días`;
    if (diffDays < 30) return `Hace ${Math.floor(diffDays / 7)} semanas`;
    if (diffDays < 365) return `Hace ${Math.floor(diffDays / 30)} meses`;
    return `Hace ${Math.floor(diffDays / 365)} años`;
};

// Truncar texto
const truncateText = (text, maxLength = 150) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + '...';
};

// Normalizar texto para búsquedas
const normalizeText = (text) => {
    return (text || '').toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remover acentos
        .trim();
};

// Generar slug para URLs
const generateSlug = (text) => {
    return normalizeText(text)
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '');
};

// Validar email
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

// Escapar HTML
const escapeHtml = (text) => {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
};

// Obtener país del usuario (simulado)
const getUserCountry = () => {
    // En una implementación real, usarías una API de geolocalización
    const countries = ['España', 'México', 'Argentina', 'Colombia', 'Chile', 'Perú'];
    return countries[Math.floor(Math.random() * countries.length)];
};

// Debounce para optimizar búsquedas
const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Animaciones suaves
const smoothScrollTo = (element, offset = 0) => {
    const elementPosition = element.offsetTop - offset;
    window.scrollTo({
        top: elementPosition,
        behavior: 'smooth'
    });
};

// Gestión de modales
const openModal = (modalId) => {
    const modal = $(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        
        // Enfocar el primer elemento focuseable
        const focusable = modal.querySelector('input, textarea, button, [tabindex]');
        if (focusable) {
            setTimeout(() => focusable.focus(), 100);
        }
    }
};

const closeModal = (modalId) => {
    const modal = $(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = '';
    }
};

// Gestión de formularios
const resetForm = (form) => {
    if (form) {
        form.reset();
        // Limpiar errores de validación
        $$('.error-message', form).forEach(error => error.remove());
        $$('.border-red-500', form).forEach(input => {
            input.classList.remove('border-red-500');
        });
    }
};

const showFieldError = (field, message) => {
    // Remover error anterior
    const existingError = field.parentNode.querySelector('.error-message');
    if (existingError) existingError.remove();
    
    // Agregar nuevo error
    field.classList.add('border-red-500');
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message text-red-400 text-sm mt-1';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
};

const clearFieldError = (field) => {
    field.classList.remove('border-red-500');
    const error = field.parentNode.querySelector('.error-message');
    if (error) error.remove();
};

// Gestión de estado de carga
const setLoading = (element, loading = true) => {
    if (loading) {
        element.disabled = true;
        element.dataset.originalText = element.textContent;
        element.innerHTML = `
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Cargando...
        `;
    } else {
        element.disabled = false;
        element.textContent = element.dataset.originalText || 'Enviar';
    }
};

// Copiar al portapapeles
const copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        toast('Copiado al portapapeles');
    } catch (err) {
        // Fallback para navegadores más antiguos
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        toast('Copiado al portapapeles');
    }
};

// Formatear números
const formatNumber = (num) => {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
};

// Generar colores aleatorios para gráficos
const generateColors = (count) => {
    const colors = [
        'rgba(244, 114, 182, 0.8)', // softpink
        'rgba(167, 139, 250, 0.8)', // lavender
        'rgba(34, 197, 94, 0.8)',   // green
        'rgba(59, 130, 246, 0.8)',  // blue
        'rgba(245, 158, 11, 0.8)',  // amber
        'rgba(239, 68, 68, 0.8)',   // red
        'rgba(139, 92, 246, 0.8)',  // violet
        'rgba(6, 182, 212, 0.8)',   // cyan
    ];
    
    const result = [];
    for (let i = 0; i < count; i++) {
        result.push(colors[i % colors.length]);
    }
    return result;
};

// Detectar dispositivo móvil
const isMobile = () => {
    return window.innerWidth < 768;
};

// Lazy loading de imágenes
const setupLazyLoading = () => {
    const images = $$('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback para navegadores sin soporte
        images.forEach(img => {
            img.src = img.dataset.src;
            img.classList.remove('lazy');
        });
    }
};

// Inicializar utilidades cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    // Configurar año actual
    const yearElement = $('#year');
    if (yearElement) {
        yearElement.textContent = new Date().getFullYear();
    }
    
    // Configurar lazy loading
    setupLazyLoading();
    
    // Registrar visita
    if (window.EcosDB) {
        EcosDB.recordVisit(getUserCountry());
    }
});

// Exportar utilidades para uso global
window.EcosUtils = {
    $, $$, toast, formatDate, formatRelativeDate, truncateText, normalizeText,
    generateSlug, isValidEmail, escapeHtml, getUserCountry, debounce,
    smoothScrollTo, openModal, closeModal, resetForm, showFieldError,
    clearFieldError, setLoading, copyToClipboard, formatNumber,
    generateColors, isMobile, setupLazyLoading
};
