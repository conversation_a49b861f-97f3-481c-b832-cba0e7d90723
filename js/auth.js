// Sistema de Autenticación para Ecos del Alma

class AuthSystem {
    constructor() {
        this.isAuthenticated = false;
        this.sessionKey = 'ecos_admin_session';
        this.passwordKey = 'ecos_admin_password';
        this.defaultPassword = 'EcosDelAlma2024!';
        this.sessionDuration = 24 * 60 * 60 * 1000; // 24 horas
        
        this.init();
    }

    init() {
        this.checkStoredPassword();
        this.checkSession();
    }

    checkStoredPassword() {
        const storedPassword = localStorage.getItem(this.passwordKey);
        if (!storedPassword) {
            localStorage.setItem(this.passwordKey, this.defaultPassword);
        }
    }

    checkSession() {
        const session = localStorage.getItem(this.sessionKey);
        if (session) {
            try {
                const sessionData = JSON.parse(session);
                const now = new Date().getTime();
                
                if (sessionData.expires > now) {
                    this.isAuthenticated = true;
                    return true;
                } else {
                    this.logout();
                }
            } catch (error) {
                this.logout();
            }
        }
        return false;
    }

    async login(password) {
        const storedPassword = localStorage.getItem(this.passwordKey);
        
        // Simular delay de autenticación
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        if (password === storedPassword) {
            const now = new Date().getTime();
            const sessionData = {
                authenticated: true,
                timestamp: now,
                expires: now + this.sessionDuration
            };
            
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
            this.isAuthenticated = true;
            
            // Registrar login en analytics
            if (window.EcosDB) {
                EcosDB.recordAdminLogin();
            }
            
            return { success: true, message: 'Acceso concedido' };
        } else {
            return { success: false, message: 'Contraseña incorrecta' };
        }
    }

    logout() {
        localStorage.removeItem(this.sessionKey);
        this.isAuthenticated = false;

        // Redirigir a login si estamos en página admin
        const currentPath = window.location.pathname;
        if (currentPath.includes('admin.html') || currentPath.includes('editor.html')) {
            window.location.href = 'admin/index.html';
        } else if (currentPath.includes('/admin/')) {
            window.location.href = 'index.html';
        }
    }

    changePassword(currentPassword, newPassword) {
        const storedPassword = localStorage.getItem(this.passwordKey);
        
        if (currentPassword !== storedPassword) {
            return { success: false, message: 'Contraseña actual incorrecta' };
        }
        
        if (newPassword.length < 8) {
            return { success: false, message: 'La nueva contraseña debe tener al menos 8 caracteres' };
        }
        
        localStorage.setItem(this.passwordKey, newPassword);
        return { success: true, message: 'Contraseña cambiada exitosamente' };
    }

    requireAuth() {
        if (!this.isAuthenticated) {
            this.redirectToLogin();
            return false;
        }
        return true;
    }

    redirectToLogin() {
        const currentPath = window.location.pathname;
        const isAlreadyOnLogin = currentPath.endsWith('/admin') || currentPath.endsWith('/admin/') || currentPath.includes('/admin/index.html');

        if (!isAlreadyOnLogin) {
            window.location.href = 'admin/index.html';
        }
    }

    extendSession() {
        if (this.isAuthenticated) {
            const now = new Date().getTime();
            const sessionData = {
                authenticated: true,
                timestamp: now,
                expires: now + this.sessionDuration
            };
            localStorage.setItem(this.sessionKey, JSON.stringify(sessionData));
        }
    }
}

// Crear instancia global
window.AuthSystem = new AuthSystem();

// Funciones de utilidad para templates
window.isAuthenticated = () => window.AuthSystem.isAuthenticated;
window.requireAuth = () => window.AuthSystem.requireAuth();
window.logout = () => window.AuthSystem.logout();

// Auto-extender sesión en actividad (deshabilitado para evitar problemas)
// let activityTimer;
// function resetActivityTimer() {
//     clearTimeout(activityTimer);
//     activityTimer = setTimeout(() => {
//         if (window.AuthSystem.isAuthenticated) {
//             window.AuthSystem.extendSession();
//         }
//     }, 5 * 60 * 1000); // 5 minutos
// }

// Escuchar actividad del usuario (deshabilitado)
// ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
//     document.addEventListener(event, resetActivityTimer, true);
// });

// Verificar autenticación en páginas admin
document.addEventListener('DOMContentLoaded', () => {
    const currentPath = window.location.pathname;
    const isLoginPage = currentPath.endsWith('/admin') || currentPath.endsWith('/admin/') || currentPath.includes('/admin/index.html');
    const isAdminPage = currentPath.includes('admin.html') || currentPath.includes('editor.html');

    // Si estamos en la página de login y ya estamos autenticados, redirigir al admin
    if (isLoginPage && window.AuthSystem.isAuthenticated) {
        window.location.href = '../admin.html';
        return;
    }

    // Si estamos en una página admin y no estamos autenticados, redirigir al login
    if (isAdminPage && !window.AuthSystem.isAuthenticated) {
        window.location.href = 'admin/index.html';
        return;
    }
});

// Funciones para el formulario de login
window.handleLogin = async (event) => {
    event.preventDefault();
    
    const form = event.target;
    const password = form.password.value;
    const submitBtn = form.querySelector('button[type="submit"]');
    const errorMsg = document.getElementById('errorMessage');
    
    // Mostrar loading
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<div class="loading-magical"></div> Verificando...';
    
    try {
        const result = await window.AuthSystem.login(password);
        
        if (result.success) {
            // Mostrar éxito
            submitBtn.innerHTML = '✓ Acceso concedido';
            submitBtn.classList.add('bg-green-500');
            
            // Redirigir después de un momento
            setTimeout(() => {
                window.location.href = 'admin.html';
            }, 1000);
        } else {
            // Mostrar error
            if (errorMsg) {
                errorMsg.textContent = result.message;
                errorMsg.classList.remove('hidden');
            }
            
            // Resetear botón
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Iniciar Sesión';
            
            // Limpiar campo
            form.password.value = '';
            form.password.focus();
            
            // Ocultar error después de 3 segundos
            setTimeout(() => {
                if (errorMsg) {
                    errorMsg.classList.add('hidden');
                }
            }, 3000);
        }
    } catch (error) {
        console.error('Error en login:', error);
        
        if (errorMsg) {
            errorMsg.textContent = 'Error de conexión. Intenta nuevamente.';
            errorMsg.classList.remove('hidden');
        }
        
        submitBtn.disabled = false;
        submitBtn.innerHTML = 'Iniciar Sesión';
    }
};

// Función para cambiar contraseña desde admin
window.handlePasswordChange = async (event) => {
    event.preventDefault();
    
    const form = event.target;
    const currentPassword = form.currentPassword.value;
    const newPassword = form.newPassword.value;
    const confirmPassword = form.confirmPassword.value;
    
    if (newPassword !== confirmPassword) {
        if (window.EcosUtils) {
            EcosUtils.toast('Las contraseñas no coinciden', 'error');
        }
        return;
    }
    
    const result = window.AuthSystem.changePassword(currentPassword, newPassword);
    
    if (window.EcosUtils) {
        EcosUtils.toast(result.message, result.success ? 'success' : 'error');
    }
    
    if (result.success) {
        form.reset();
    }
};

// Middleware para proteger rutas
window.protectRoute = () => {
    if (!window.AuthSystem.requireAuth()) {
        return false;
    }
    return true;
};

// Función para mostrar/ocultar elementos según autenticación
window.showIfAuthenticated = (selector) => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => {
        if (window.AuthSystem.isAuthenticated) {
            el.classList.remove('hidden');
        } else {
            el.classList.add('hidden');
        }
    });
};

// Auto-logout en inactividad prolongada (deshabilitado para evitar problemas)
// let inactivityTimer;
// function startInactivityTimer() {
//     clearTimeout(inactivityTimer);
//     inactivityTimer = setTimeout(() => {
//         if (window.AuthSystem.isAuthenticated) {
//             if (window.EcosUtils) {
//                 EcosUtils.toast('Sesión cerrada por inactividad', 'warning');
//             }
//             window.AuthSystem.logout();
//         }
//     }, 60 * 60 * 1000); // 1 hora
// }

// Reiniciar timer en actividad (deshabilitado)
// ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
//     document.addEventListener(event, startInactivityTimer, true);
// });

// Iniciar timer (deshabilitado)
// startInactivityTimer();
