// JavaScript para el Panel de Administración de Ecos del Alma

class AdminPanel {
    constructor() {
        this.currentSection = 'dashboard';
        this.charts = {};
        this.blogs = [];
        this.subscribers = [];
        this.messages = [];
        this.analytics = {};
        this.init();
    }

    init() {
        console.log('Inicializando AdminPanel...');
        this.loadData();
        this.loadDashboard();
        this.setupEventListeners();
        this.updateNotifications();
        console.log('AdminPanel inicializado correctamente');
    }

    loadData() {
        // Cargar todos los datos desde la base de datos local
        this.blogs = window.EcosDB ? window.EcosDB.getBlogs() : [];
        this.subscribers = window.EcosDB ? window.EcosDB.getSubscribers() : [];
        this.messages = window.EcosDB ? window.EcosDB.getMessages() : [];
        this.analytics = window.EcosDB ? window.EcosDB.getAnalytics() : {};
        console.log('Datos cargados:', {
            blogs: this.blogs.length,
            subscribers: this.subscribers.length,
            messages: this.messages.length
        });
    }

    setupEventListeners() {
        // Mobile menu toggle
        const mobileMenuBtn = $('#mobileMenuBtn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', this.toggleMobileMenu);
        }

        // Cerrar modales con ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });

        // Logout button
        const logoutBtn = $('#logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', this.handleLogout.bind(this));
        }
    }

    // Función para logout
    handleLogout() {
        if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
            localStorage.removeItem('ecos_admin_session');
            window.location.href = 'admin-direct.html';
        }
    }

    // Función para mostrar toast/notificaciones
    showToast(message, type = 'info') {
        console.log(`Toast [${type}]: ${message}`);
        if (window.EcosUtils && window.EcosUtils.toast) {
            window.EcosUtils.toast(message, type);
        } else {
            alert(message);
        }
    }

    // Función para formatear fechas
    formatDate(dateString) {
        if (window.EcosUtils && window.EcosUtils.formatDate) {
            return window.EcosUtils.formatDate(dateString);
        }
        return new Date(dateString).toLocaleDateString('es-ES');
    }

    // Navegación entre secciones
    showSection(sectionName) {
        console.log('Cambiando a sección:', sectionName);

        // Ocultar todas las secciones
        $$('.admin-section').forEach(section => {
            section.classList.add('hidden');
        });

        // Remover clase active de todos los items del sidebar
        $$('.sidebar-item').forEach(item => {
            item.classList.remove('active');
        });

        // Mostrar la sección seleccionada
        const targetSection = $(`#${sectionName}-section`);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            this.currentSection = sectionName;
        }

        // Activar el item del sidebar correspondiente
        const activeItem = $(`[onclick="showSection('${sectionName}')"]`);

        // Cargar contenido específico de la sección
        switch(sectionName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'blogs':
                this.loadBlogsSection();
                break;
            case 'subscribers':
                this.loadSubscribersSection();
                break;
            case 'messages':
                this.loadMessagesSection();
                break;
            case 'analytics':
                this.loadAnalyticsSection();
                break;
            case 'settings':
                this.loadSettingsSection();
                break;
        }
        if (activeItem) {
            activeItem.classList.add('active');
        }

        this.currentSection = sectionName;

        // Cargar contenido específico de la sección
        switch (sectionName) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'blogs':
                this.loadBlogsSection();
                break;
            case 'subscribers':
                this.loadSubscribersSection();
                break;
            case 'messages':
                this.loadMessagesSection();
                break;
            case 'analytics':
                this.loadAnalyticsSection();
                break;
            case 'settings':
                this.loadSettingsSection();
                break;
        }
    }

    // Dashboard
    loadDashboard() {
        this.updateDashboardStats();
        this.loadRecentBlogs();
        this.createCharts();
    }

    updateDashboardStats() {
        const blogs = EcosDB.getBlogs();
        const subscribers = EcosDB.getSubscribers();
        const messages = EcosDB.getMessages();
        const analytics = EcosDB.getAnalytics();

        const totalBlogs = blogs.length;
        const totalViews = blogs.reduce((sum, blog) => sum + (blog.views || 0), 0);
        const totalSubscribers = subscribers.length;
        const totalMessages = messages.length;

        // Actualizar elementos del DOM
        const dashTotalBlogs = $('#dashTotalBlogs');
        const dashTotalViews = $('#dashTotalViews');
        const dashTotalSubscribers = $('#dashTotalSubscribers');
        const dashTotalMessages = $('#dashTotalMessages');

        if (dashTotalBlogs) dashTotalBlogs.textContent = totalBlogs;
        if (dashTotalViews) dashTotalViews.textContent = EcosUtils.formatNumber(totalViews);
        if (dashTotalSubscribers) dashTotalSubscribers.textContent = totalSubscribers;
        if (dashTotalMessages) dashTotalMessages.textContent = totalMessages;
    }

    loadRecentBlogs() {
        const blogs = EcosDB.getBlogs().slice(0, 5);
        const recentBlogs = $('#recentBlogs');
        
        if (!recentBlogs) return;

        if (blogs.length === 0) {
            recentBlogs.innerHTML = `
                <div class="text-center py-4 text-white/60">
                    <p>No hay blogs aún</p>
                    <button onclick="adminPanel.openBlogEditor()" class="mt-2 text-softpink hover:text-softpink-deep transition">
                        Crear tu primer blog
                    </button>
                </div>
            `;
            return;
        }

        recentBlogs.innerHTML = blogs.map(blog => `
            <div class="flex items-center justify-between p-3 glass rounded-xl">
                <div class="flex-1">
                    <h4 class="font-medium text-sm">${EcosUtils.escapeHtml(blog.title)}</h4>
                    <p class="text-xs text-white/60">${EcosUtils.formatRelativeDate(blog.date)}</p>
                </div>
                <div class="flex items-center gap-2">
                    <span class="text-xs text-white/60">${EcosUtils.formatNumber(blog.views || 0)} vistas</span>
                    <button onclick="adminPanel.editBlog('${blog.id}')" class="text-softpink hover:text-softpink-deep transition">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="1.5"/>
                            <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="1.5"/>
                        </svg>
                    </button>
                </div>
            </div>
        `).join('');
    }

    createCharts() {
        this.createDailyVisitsChart();
        this.createCountriesChart();
    }

    createDailyVisitsChart() {
        const canvas = $('#dailyVisitsChart');
        if (!canvas) return;

        const analytics = EcosDB.getAnalytics();
        const dailyVisits = analytics.dailyVisits || {};
        
        // Obtener los últimos 7 días
        const labels = [];
        const data = [];
        const today = new Date();
        
        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(date.getDate() - i);
            const dateStr = date.toISOString().split('T')[0];
            const dayName = date.toLocaleDateString('es-ES', { weekday: 'short' });
            
            labels.push(dayName);
            data.push(dailyVisits[dateStr] || 0);
        }

        if (this.charts.dailyVisits) {
            this.charts.dailyVisits.destroy();
        }

        this.charts.dailyVisits = new Chart(canvas, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Visitas',
                    data: data,
                    borderColor: '#f472b6',
                    backgroundColor: 'rgba(244, 114, 182, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: 'rgba(255,255,255,0.7)'
                        },
                        grid: {
                            color: 'rgba(255,255,255,0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'rgba(255,255,255,0.7)'
                        },
                        grid: {
                            color: 'rgba(255,255,255,0.1)'
                        }
                    }
                }
            }
        });
    }

    createCountriesChart() {
        const canvas = $('#countriesChart');
        if (!canvas) return;

        const analytics = EcosDB.getAnalytics();
        const countries = analytics.countries || {};
        
        const labels = Object.keys(countries).slice(0, 5);
        const data = labels.map(country => countries[country]);
        const colors = EcosUtils.generateColors(labels.length);

        if (this.charts.countries) {
            this.charts.countries.destroy();
        }

        this.charts.countries = new Chart(canvas, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: 'rgba(255,255,255,0.8)',
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }

    // Gestión de Blogs
    loadBlogsSection() {
        const blogs = EcosDB.getBlogs();
        const blogsList = $('#blogsList');
        
        if (!blogsList) return;

        if (blogs.length === 0) {
            blogsList.innerHTML = `
                <div class="glass rounded-2xl p-8 text-center">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto mb-4 text-white/50">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="1.5"/>
                        <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                    <h3 class="font-display text-xl mb-2">No hay blogs aún</h3>
                    <p class="text-white/70 mb-4">Comienza creando tu primer blog</p>
                    <button onclick="adminPanel.openBlogEditor()" class="bg-softpink hover:bg-softpink-deep px-6 py-3 rounded-xl transition shadow-glow">
                        Crear Primer Blog
                    </button>
                </div>
            `;
            return;
        }

        blogsList.innerHTML = blogs.map(blog => this.createBlogListItem(blog)).join('');
    }

    createBlogListItem(blog) {
        return `
            <div class="glass rounded-2xl p-6">
                <div class="flex items-start justify-between gap-4">
                    <div class="flex-1">
                        <div class="flex items-center gap-3 mb-2">
                            <h3 class="font-display text-xl">${EcosUtils.escapeHtml(blog.title)}</h3>
                            <span class="text-xs px-2 py-1 rounded-full ${blog.published ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400'}">
                                ${blog.published ? 'Publicado' : 'Borrador'}
                            </span>
                            ${blog.featured ? '<span class="text-xs px-2 py-1 rounded-full bg-softpink/20 text-softpink">Destacado</span>' : ''}
                        </div>
                        <p class="text-white/80 text-sm mb-3">${EcosUtils.truncateText(blog.excerpt || this.extractExcerpt(blog.content), 150)}</p>
                        <div class="flex items-center gap-4 text-white/60 text-sm">
                            <span>${EcosUtils.formatDate(blog.date)}</span>
                            <span>${EcosUtils.formatNumber(blog.views || 0)} vistas</span>
                            <span class="capitalize">${blog.category || 'Sin categoría'}</span>
                        </div>
                        ${blog.tags && blog.tags.length > 0 ? `
                            <div class="flex flex-wrap gap-1 mt-2">
                                ${blog.tags.map(tag => `<span class="text-xs px-2 py-1 rounded-full bg-white/10">${EcosUtils.escapeHtml(tag)}</span>`).join('')}
                            </div>
                        ` : ''}
                    </div>
                    <div class="flex items-center gap-2">
                        <button onclick="adminPanel.editBlog('${blog.id}')" class="p-2 rounded-xl glass hover:bg-white/20 transition" title="Editar">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="1.5"/>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                        </button>
                        <button onclick="adminPanel.toggleBlogPublished('${blog.id}')" class="p-2 rounded-xl glass hover:bg-white/20 transition" title="${blog.published ? 'Despublicar' : 'Publicar'}">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                ${blog.published ? 
                                    '<path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="1.5"/><circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5"/>' :
                                    '<path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="1.5"/><line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="1.5"/>'
                                }
                            </svg>
                        </button>
                        <button onclick="adminPanel.deleteBlog('${blog.id}')" class="p-2 rounded-xl glass hover:bg-red-500/20 transition text-red-400" title="Eliminar">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="1.5"/>
                                <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    extractExcerpt(content) {
        const div = document.createElement('div');
        div.innerHTML = content;
        return div.textContent || div.innerText || '';
    }

    openBlogEditor(blogId = null) {
        // Redirigir al editor de blogs
        if (blogId) {
            window.location.href = `editor.html?id=${blogId}`;
        } else {
            window.location.href = 'editor.html';
        }
    }

    editBlog(blogId) {
        this.openBlogEditor(blogId);
    }

    toggleBlogPublished(blogId) {
        const blogs = EcosDB.getBlogs();
        const blog = blogs.find(b => b.id === blogId);
        
        if (blog) {
            blog.published = !blog.published;
            EcosDB.saveBlog(blog);
            this.loadBlogsSection();
            EcosUtils.toast(blog.published ? 'Blog publicado' : 'Blog despublicado');
        }
    }

    deleteBlog(blogId) {
        if (confirm('¿Estás seguro de que quieres eliminar este blog? Esta acción no se puede deshacer.')) {
            EcosDB.deleteBlog(blogId);
            this.loadBlogsSection();
            this.updateDashboardStats();
            EcosUtils.toast('Blog eliminado');
        }
    }

    // Suscriptores
    loadSubscribersSection() {
        const subscribers = EcosDB.getSubscribers();
        const subscribersList = $('#subscribersList');

        if (!subscribersList) return;

        if (subscribers.length === 0) {
            subscribersList.innerHTML = `
                <div class="glass rounded-2xl p-8 text-center">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto mb-4 text-white/50">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="1.5"/>
                        <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                    <h3 class="font-display text-xl mb-2">No hay suscriptores aún</h3>
                    <p class="text-white/70">Los suscriptores aparecerán aquí cuando se registren</p>
                </div>
            `;
            return;
        }

        subscribersList.innerHTML = `
            <div class="glass rounded-2xl overflow-hidden">
                <div class="p-6 border-b border-white/10">
                    <h3 class="font-display text-xl">Suscriptores (${subscribers.length})</h3>
                </div>
                <div class="divide-y divide-white/10">
                    ${subscribers.map(subscriber => `
                        <div class="p-4 flex items-center justify-between">
                            <div>
                                <h4 class="font-medium">${EcosUtils.escapeHtml(subscriber.name || 'Sin nombre')}</h4>
                                <p class="text-white/70 text-sm">${EcosUtils.escapeHtml(subscriber.email)}</p>
                                <p class="text-white/50 text-xs">${EcosUtils.formatDate(subscriber.date)}</p>
                            </div>
                            <button onclick="adminPanel.deleteSubscriber('${subscriber.id}')" class="p-2 rounded-xl glass hover:bg-red-500/20 transition text-red-400" title="Eliminar">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                    <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="1.5"/>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="1.5"/>
                                </svg>
                            </button>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    deleteSubscriber(subscriberId) {
        if (confirm('¿Estás seguro de que quieres eliminar este suscriptor?')) {
            EcosDB.deleteSubscriber(subscriberId);
            this.loadSubscribersSection();
            this.updateDashboardStats();
            EcosUtils.toast('Suscriptor eliminado');
        }
    }

    // Mensajes
    loadMessagesSection() {
        const messages = EcosDB.getMessages();
        const messagesList = $('#messagesList');

        if (!messagesList) return;

        if (messages.length === 0) {
            messagesList.innerHTML = `
                <div class="glass rounded-2xl p-8 text-center">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto mb-4 text-white/50">
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="1.5"/>
                        <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="1.5"/>
                    </svg>
                    <h3 class="font-display text-xl mb-2">No hay mensajes aún</h3>
                    <p class="text-white/70">Los mensajes de contacto aparecerán aquí</p>
                </div>
            `;
            return;
        }

        messagesList.innerHTML = `
            <div class="space-y-4">
                ${messages.map(message => `
                    <div class="glass rounded-2xl p-6 ${!message.read ? 'border-l-4 border-softpink' : ''}">
                        <div class="flex items-start justify-between gap-4">
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <h4 class="font-medium">${EcosUtils.escapeHtml(message.name || 'Sin nombre')}</h4>
                                    <span class="text-sm text-white/60">${EcosUtils.escapeHtml(message.email)}</span>
                                    ${!message.read ? '<span class="text-xs px-2 py-1 rounded-full bg-softpink/20 text-softpink">Nuevo</span>' : ''}
                                </div>
                                <p class="text-white/80 whitespace-pre-wrap">${EcosUtils.escapeHtml(message.message)}</p>
                                <p class="text-white/50 text-sm mt-2">${EcosUtils.formatDate(message.date)}</p>
                            </div>
                            <div class="flex items-center gap-2">
                                ${!message.read ? `
                                    <button onclick="adminPanel.markMessageAsRead('${message.id}')" class="p-2 rounded-xl glass hover:bg-white/20 transition" title="Marcar como leído">
                                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                            <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="1.5"/>
                                        </svg>
                                    </button>
                                ` : ''}
                                <button onclick="adminPanel.deleteMessage('${message.id}')" class="p-2 rounded-xl glass hover:bg-red-500/20 transition text-red-400" title="Eliminar">
                                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                                        <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="1.5"/>
                                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="1.5"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    markMessageAsRead(messageId) {
        EcosDB.markMessageAsRead(messageId);
        this.loadMessagesSection();
        this.updateNotifications();
        EcosUtils.toast('Mensaje marcado como leído');
    }

    deleteMessage(messageId) {
        if (confirm('¿Estás seguro de que quieres eliminar este mensaje?')) {
            EcosDB.deleteMessage(messageId);
            this.loadMessagesSection();
            this.updateDashboardStats();
            this.updateNotifications();
            EcosUtils.toast('Mensaje eliminado');
        }
    }

    // Analytics
    loadAnalyticsSection() {
        const analyticsContent = $('#analyticsContent');
        if (!analyticsContent) return;

        const analytics = EcosDB.getAnalytics();
        const blogs = EcosDB.getBlogs();

        analyticsContent.innerHTML = `
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="glass rounded-2xl p-6">
                    <h3 class="font-display text-xl mb-4">Resumen General</h3>
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-white/70">Total de visitas:</span>
                            <span class="font-bold text-softpink">${EcosUtils.formatNumber(analytics.totalVisits || 0)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-white/70">Visitantes únicos:</span>
                            <span class="font-bold text-softpink">${EcosUtils.formatNumber(analytics.uniqueVisitors || 0)}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-white/70">Blogs publicados:</span>
                            <span class="font-bold text-softpink">${blogs.filter(b => b.published).length}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-white/70">Total de vistas de blogs:</span>
                            <span class="font-bold text-softpink">${EcosUtils.formatNumber(blogs.reduce((sum, blog) => sum + (blog.views || 0), 0))}</span>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-2xl p-6">
                    <h3 class="font-display text-xl mb-4">Top Países</h3>
                    <div class="space-y-3">
                        ${Object.entries(analytics.countries || {})
                            .sort(([,a], [,b]) => b - a)
                            .slice(0, 5)
                            .map(([country, visits]) => `
                                <div class="flex justify-between items-center">
                                    <span class="text-white/80">${country}</span>
                                    <div class="flex items-center gap-2">
                                        <div class="w-20 h-2 bg-white/10 rounded-full overflow-hidden">
                                            <div class="h-full bg-softpink rounded-full" style="width: ${(visits / Math.max(...Object.values(analytics.countries || {}))) * 100}%"></div>
                                        </div>
                                        <span class="text-sm text-white/60 w-8 text-right">${visits}</span>
                                    </div>
                                </div>
                            `).join('')}
                    </div>
                </div>
            </div>

            <div class="glass rounded-2xl p-6">
                <h3 class="font-display text-xl mb-4">Blogs Más Populares</h3>
                <div class="space-y-3">
                    ${blogs
                        .filter(blog => blog.published)
                        .sort((a, b) => (b.views || 0) - (a.views || 0))
                        .slice(0, 10)
                        .map(blog => `
                            <div class="flex justify-between items-center p-3 glass rounded-xl">
                                <div class="flex-1">
                                    <h4 class="font-medium text-sm">${EcosUtils.escapeHtml(blog.title)}</h4>
                                    <p class="text-xs text-white/60">${EcosUtils.formatDate(blog.date)}</p>
                                </div>
                                <div class="text-right">
                                    <span class="text-softpink font-bold">${EcosUtils.formatNumber(blog.views || 0)}</span>
                                    <p class="text-xs text-white/60">vistas</p>
                                </div>
                            </div>
                        `).join('')}
                </div>
            </div>
        `;
    }

    // Configuración
    loadSettingsSection() {
        const settingsContent = $('#settingsContent');
        if (!settingsContent) return;

        const settings = EcosDB.getSettings();

        settingsContent.innerHTML = `
            <form id="settingsForm" class="space-y-6">
                <div class="glass rounded-2xl p-6">
                    <h3 class="font-display text-xl mb-4">Información del Sitio</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Nombre del Sitio</label>
                            <input type="text" name="siteName" value="${EcosUtils.escapeHtml(settings.siteName || '')}"
                                   class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink">
                        </div>
                        <div>
                            <label class="block text-sm font-medium mb-2">Email de Contacto</label>
                            <input type="email" name="contactEmail" value="${EcosUtils.escapeHtml(settings.contactEmail || '')}"
                                   class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium mb-2">Descripción del Sitio</label>
                            <textarea name="siteDescription" rows="3"
                                      class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink">${EcosUtils.escapeHtml(settings.siteDescription || '')}</textarea>
                        </div>
                    </div>
                </div>

                <div class="glass rounded-2xl p-6">
                    <h3 class="font-display text-xl mb-4">Información del Autor</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">Nombre del Autor</label>
                            <input type="text" name="authorName" value="${EcosUtils.escapeHtml(settings.authorName || '')}"
                                   class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink">
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium mb-2">Biografía del Autor</label>
                            <textarea name="authorBio" rows="3"
                                      class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink">${EcosUtils.escapeHtml(settings.authorBio || '')}</textarea>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="bg-softpink hover:bg-softpink-deep px-6 py-3 rounded-xl transition shadow-glow">
                        Guardar Configuración
                    </button>
                </div>
            </form>
        `;

        // Agregar event listener para el formulario
        const settingsForm = $('#settingsForm');
        if (settingsForm) {
            settingsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveSettings(new FormData(settingsForm));
            });
        }
    }

    saveSettings(formData) {
        const settings = EcosDB.getSettings();

        settings.siteName = formData.get('siteName');
        settings.siteDescription = formData.get('siteDescription');
        settings.authorName = formData.get('authorName');
        settings.authorBio = formData.get('authorBio');
        settings.contactEmail = formData.get('contactEmail');

        EcosDB.saveSettings(settings);
        EcosUtils.toast('Configuración guardada exitosamente');
    }

    updateNotifications() {
        const messages = EcosDB.getMessages();
        const unreadMessages = messages.filter(m => !m.read).length;

        const notificationBadge = $('#notificationBadge');
        const messagesBadge = $('#messagesBadge');

        if (notificationBadge) {
            if (unreadMessages > 0) {
                notificationBadge.textContent = unreadMessages;
                notificationBadge.classList.remove('hidden');
            } else {
                notificationBadge.classList.add('hidden');
            }
        }

        if (messagesBadge) {
            if (unreadMessages > 0) {
                messagesBadge.textContent = unreadMessages;
                messagesBadge.classList.remove('hidden');
            } else {
                messagesBadge.classList.add('hidden');
            }
        }
    }

    showNotifications() {
        EcosUtils.toast('Panel de notificaciones próximamente disponible');
    }

    toggleMobileMenu() {
        EcosUtils.toast('Menú móvil próximamente disponible');
    }

    closeAllModals() {
        // Cerrar cualquier modal abierto
        $$('.modal').forEach(modal => {
            modal.classList.add('hidden');
        });
        document.body.style.overflow = '';
    }
}

// Funciones globales
window.showSection = (section) => {
    if (window.adminPanel) {
        window.adminPanel.showSection(section);
    }
};

window.showNotifications = () => {
    if (window.adminPanel) {
        window.adminPanel.showNotifications();
    }
};

window.logout = () => {
    if (window.adminPanel) {
        window.adminPanel.handleLogout();
    }
};

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    console.log('Inicializando AdminPanel...');
    window.adminPanel = new AdminPanel();
    console.log('AdminPanel inicializado correctamente');
});
