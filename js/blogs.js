// JavaScript para la página de blogs de Ecos del Alma

class BlogsPage {
    constructor() {
        this.blogs = [];
        this.filteredBlogs = [];
        this.currentPage = 1;
        this.blogsPerPage = 9;
        this.currentFilter = 'all';
        this.currentSort = 'date';
        this.searchQuery = '';
        this.currentBlogIndex = -1;
        
        this.init();
    }

    init() {
        this.loadBlogs();
        this.setupEventListeners();
        this.updateStats();
        this.renderBlogs();
    }

    loadBlogs() {
        this.blogs = EcosDB.getBlogs().filter(blog => blog.published);
        this.filteredBlogs = [...this.blogs];
    }

    setupEventListeners() {
        // Búsqueda
        const searchInput = $('#searchInput');
        const clearSearch = $('#clearSearch');
        
        if (searchInput) {
            searchInput.addEventListener('input', EcosUtils.debounce(() => {
                this.searchQuery = searchInput.value.trim();
                this.applyFilters();
                clearSearch.classList.toggle('hidden', !this.searchQuery);
            }, 300));
        }

        if (clearSearch) {
            clearSearch.addEventListener('click', () => {
                searchInput.value = '';
                this.searchQuery = '';
                this.applyFilters();
                clearSearch.classList.add('hidden');
            });
        }

        // Filtros de categoría
        $$('#filters .filter-chip').forEach(btn => {
            btn.addEventListener('click', () => {
                $$('#filters .filter-chip').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentFilter = btn.dataset.filter;
                this.applyFilters();
            });
        });

        // Ordenamiento
        const sortBy = $('#sortBy');
        if (sortBy) {
            sortBy.addEventListener('change', () => {
                this.currentSort = sortBy.value;
                this.applyFilters();
            });
        }

        // Modal de navegación
        const modalPrev = $('#modalPrev');
        const modalNext = $('#modalNext');
        
        if (modalPrev) {
            modalPrev.addEventListener('click', () => this.navigateBlog(-1));
        }
        
        if (modalNext) {
            modalNext.addEventListener('click', () => this.navigateBlog(1));
        }

        // Mobile menu
        window.toggleMobileMenu = () => {
            $('#mobileMenu').classList.toggle('hidden');
        };

        // Cerrar modal con ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeBlogModal();
            }
        });
    }

    applyFilters() {
        let filtered = [...this.blogs];

        // Aplicar filtro de búsqueda
        if (this.searchQuery) {
            const query = EcosUtils.normalizeText(this.searchQuery);
            filtered = filtered.filter(blog => {
                const title = EcosUtils.normalizeText(blog.title);
                const content = EcosUtils.normalizeText(blog.content);
                const tags = blog.tags ? blog.tags.map(tag => EcosUtils.normalizeText(tag)).join(' ') : '';
                
                return title.includes(query) || 
                       content.includes(query) || 
                       tags.includes(query);
            });
        }

        // Aplicar filtro de categoría
        if (this.currentFilter !== 'all') {
            filtered = filtered.filter(blog => blog.category === this.currentFilter);
        }

        // Aplicar ordenamiento
        filtered.sort((a, b) => {
            switch (this.currentSort) {
                case 'views':
                    return (b.views || 0) - (a.views || 0);
                case 'title':
                    return a.title.localeCompare(b.title, 'es');
                case 'date':
                default:
                    return new Date(b.date) - new Date(a.date);
            }
        });

        this.filteredBlogs = filtered;
        this.currentPage = 1;
        this.renderBlogs();
        this.updateResultsCount();
    }

    updateStats() {
        const totalBlogs = this.blogs.length;
        const totalViews = this.blogs.reduce((sum, blog) => sum + (blog.views || 0), 0);
        const totalPoetry = this.blogs.filter(blog => blog.category === 'poesia').length;
        const totalReflections = this.blogs.filter(blog => blog.category === 'reflexiones').length;

        const totalBlogsEl = $('#totalBlogs');
        const totalViewsEl = $('#totalViews');
        const totalPoetryEl = $('#totalPoetry');
        const totalReflectionsEl = $('#totalReflections');

        if (totalBlogsEl) totalBlogsEl.textContent = totalBlogs;
        if (totalViewsEl) totalViewsEl.textContent = EcosUtils.formatNumber(totalViews);
        if (totalPoetryEl) totalPoetryEl.textContent = totalPoetry;
        if (totalReflectionsEl) totalReflectionsEl.textContent = totalReflections;
    }

    updateResultsCount() {
        const resultsCount = $('#resultsCount');
        if (resultsCount) {
            const total = this.filteredBlogs.length;
            if (this.searchQuery || this.currentFilter !== 'all') {
                resultsCount.textContent = `${total} resultado${total !== 1 ? 's' : ''} encontrado${total !== 1 ? 's' : ''}`;
            } else {
                resultsCount.textContent = `${total} blog${total !== 1 ? 's' : ''} en total`;
            }
        }
    }

    renderBlogs() {
        const blogsGrid = $('#blogsGrid');
        if (!blogsGrid) return;

        const startIndex = (this.currentPage - 1) * this.blogsPerPage;
        const endIndex = startIndex + this.blogsPerPage;
        const blogsToShow = this.filteredBlogs.slice(startIndex, endIndex);

        if (blogsToShow.length === 0) {
            blogsGrid.innerHTML = `
                <div class="col-span-full text-center py-12">
                    <div class="glass rounded-3xl p-8">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" class="mx-auto mb-4 text-white/50">
                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="1.5"/>
                            <path d="M21 21l-4.35-4.35" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                        <h3 class="font-display text-xl mb-2">No se encontraron blogs</h3>
                        <p class="text-white/70">Intenta con otros términos de búsqueda o filtros.</p>
                    </div>
                </div>
            `;
            this.renderPagination();
            return;
        }

        blogsGrid.innerHTML = blogsToShow.map(blog => this.createBlogCard(blog)).join('');
        this.renderPagination();
    }

    createBlogCard(blog) {
        const categoryColors = {
            'poesia': 'from-softpink/40 via-white/10 to-softpink/30',
            'reflexiones': 'from-white/10 via-softpink/30 to-white/10',
            'historias': 'from-softpink/25 via-white/10 to-softpink/25'
        };

        const categoryNames = {
            'poesia': 'Poesía',
            'reflexiones': 'Reflexiones',
            'historias': 'Historias'
        };

        const gradientClass = categoryColors[blog.category] || categoryColors['poesia'];
        const categoryName = categoryNames[blog.category] || 'Blog';

        return `
            <article class="blog-card glass rounded-3xl overflow-hidden flex flex-col cursor-pointer" 
                     onclick="blogsPage.openBlogModal('${blog.id}')">
                <div class="h-32 bg-gradient-to-r ${gradientClass}"></div>
                <div class="p-6 flex-1 flex flex-col gap-3">
                    <div class="flex items-center justify-between">
                        <span class="glass text-xs px-3 py-1 rounded-full">${categoryName}</span>
                        <span class="text-xs text-white/60">${EcosUtils.formatRelativeDate(blog.date)}</span>
                    </div>
                    <h3 class="font-display text-xl leading-tight">${EcosUtils.escapeHtml(blog.title)}</h3>
                    <p class="text-white/80 text-sm leading-relaxed flex-1">
                        ${EcosUtils.truncateText(blog.excerpt || this.extractExcerpt(blog.content), 120)}
                    </p>
                    <div class="flex flex-wrap gap-1 mb-2">
                        ${blog.tags ? blog.tags.slice(0, 3).map(tag => 
                            `<span class="text-xs px-2 py-1 rounded-full bg-white/10">${EcosUtils.escapeHtml(tag)}</span>`
                        ).join('') : ''}
                    </div>
                    <div class="flex items-center justify-between pt-2 border-t border-white/10">
                        <div class="flex items-center gap-2 text-white/60 text-sm">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="1.5"/>
                                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5"/>
                            </svg>
                            <span>${EcosUtils.formatNumber(blog.views || 0)}</span>
                        </div>
                        <button class="inline-flex items-center gap-2 rounded-xl px-3 py-2 bg-softpink hover:bg-softpink-deep transition text-sm">
                            <span>Leer</span>
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                                <path d="M5 12h14M13 5l7 7-7 7" stroke="white" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </article>
        `;
    }

    extractExcerpt(content) {
        // Extraer texto plano del HTML
        const div = document.createElement('div');
        div.innerHTML = content;
        return div.textContent || div.innerText || '';
    }

    renderPagination() {
        const pagination = $('#pagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.filteredBlogs.length / this.blogsPerPage);
        
        if (totalPages <= 1) {
            pagination.innerHTML = '';
            return;
        }

        let paginationHTML = '';

        // Botón anterior
        if (this.currentPage > 1) {
            paginationHTML += `
                <button onclick="blogsPage.goToPage(${this.currentPage - 1})" 
                        class="glass rounded-xl px-4 py-2 hover:bg-white/20 transition">
                    ← Anterior
                </button>
            `;
        }

        // Números de página
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `
                <button onclick="blogsPage.goToPage(1)" 
                        class="glass rounded-xl px-3 py-2 hover:bg-white/20 transition">1</button>
            `;
            if (startPage > 2) {
                paginationHTML += '<span class="px-2 text-white/50">...</span>';
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const isActive = i === this.currentPage;
            paginationHTML += `
                <button onclick="blogsPage.goToPage(${i})" 
                        class="rounded-xl px-3 py-2 transition ${
                            isActive ? 'bg-softpink text-white' : 'glass hover:bg-white/20'
                        }">${i}</button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += '<span class="px-2 text-white/50">...</span>';
            }
            paginationHTML += `
                <button onclick="blogsPage.goToPage(${totalPages})" 
                        class="glass rounded-xl px-3 py-2 hover:bg-white/20 transition">${totalPages}</button>
            `;
        }

        // Botón siguiente
        if (this.currentPage < totalPages) {
            paginationHTML += `
                <button onclick="blogsPage.goToPage(${this.currentPage + 1})" 
                        class="glass rounded-xl px-4 py-2 hover:bg-white/20 transition">
                    Siguiente →
                </button>
            `;
        }

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        this.currentPage = page;
        this.renderBlogs();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    openBlogModal(blogId) {
        const blog = this.blogs.find(b => b.id === blogId);
        if (!blog) return;

        // Registrar vista
        EcosDB.recordBlogView(blogId);
        
        // Actualizar stats
        this.updateStats();

        // Encontrar índice en la lista filtrada
        this.currentBlogIndex = this.filteredBlogs.findIndex(b => b.id === blogId);

        // Llenar modal
        const modalTitle = $('#modalTitle');
        const modalDate = $('#modalDate');
        const modalViews = $('#modalViews');
        const modalCategory = $('#modalCategory');
        const modalTags = $('#modalTags');
        const modalContent = $('#modalContent');

        if (modalTitle) modalTitle.textContent = blog.title;
        if (modalDate) modalDate.textContent = EcosUtils.formatDate(blog.date);
        if (modalViews) modalViews.textContent = `${EcosUtils.formatNumber(blog.views + 1)} vistas`;
        if (modalCategory) {
            const categoryNames = {
                'poesia': 'Poesía',
                'reflexiones': 'Reflexiones',
                'historias': 'Historias'
            };
            modalCategory.textContent = categoryNames[blog.category] || 'Blog';
        }

        if (modalTags) {
            modalTags.innerHTML = blog.tags ? blog.tags.map(tag => 
                `<span class="glass text-sm px-3 py-1 rounded-full">${EcosUtils.escapeHtml(tag)}</span>`
            ).join('') : '';
        }

        if (modalContent) {
            modalContent.innerHTML = blog.content;
        }

        // Mostrar modal
        const modal = $('#blogModal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }

    closeBlogModal() {
        const modal = $('#blogModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }

    navigateBlog(direction) {
        if (this.currentBlogIndex === -1) return;

        const newIndex = this.currentBlogIndex + direction;
        if (newIndex >= 0 && newIndex < this.filteredBlogs.length) {
            const nextBlog = this.filteredBlogs[newIndex];
            this.openBlogModal(nextBlog.id);
        }
    }

    shareBlog() {
        const modalTitle = $('#modalTitle');
        if (modalTitle && navigator.share) {
            navigator.share({
                title: modalTitle.textContent,
                text: 'Lee este hermoso blog en Ecos del Alma',
                url: window.location.href
            });
        } else {
            EcosUtils.copyToClipboard(window.location.href);
        }
    }
}

// Funciones globales
window.closeBlogModal = () => blogsPage.closeBlogModal();
window.shareBlog = () => blogsPage.shareBlog();

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.blogsPage = new BlogsPage();
});
