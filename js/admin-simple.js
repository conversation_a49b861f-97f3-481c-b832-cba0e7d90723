// Sistema de Admin Simplificado para Ecos del Alma

// Verificar autenticación al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('Cargando sistema admin simplificado...');
    
    // Verificar sesión
    const session = localStorage.getItem('ecos_admin_session');
    let isAuthenticated = false;
    
    if (session) {
        try {
            const sessionData = JSON.parse(session);
            const now = new Date().getTime();
            if (sessionData.expires && sessionData.expires > now) {
                isAuthenticated = true;
            }
        } catch (error) {
            console.log('Error verificando sesión:', error);
        }
    }
    
    // Si no está autenticado, crear sesión temporal
    if (!isAuthenticated) {
        const now = new Date().getTime();
        const sessionData = {
            authenticated: true,
            timestamp: now,
            expires: now + (24 * 60 * 60 * 1000) // 24 horas
        };
        localStorage.setItem('ecos_admin_session', JSON.stringify(sessionData));
        console.log('Sesión temporal creada');
    }
    
    // Inicializar admin
    initializeAdmin();
});

// Función para inicializar el admin
function initializeAdmin() {
    console.log('Inicializando admin...');
    
    // Cargar datos iniciales
    loadDashboardData();
    
    // Configurar event listeners
    setupEventListeners();
    
    console.log('Admin inicializado correctamente');
}

// Cargar datos del dashboard
function loadDashboardData() {
    try {
        const blogs = window.EcosDB ? window.EcosDB.getBlogs() : [];
        const subscribers = window.EcosDB ? window.EcosDB.getSubscribers() : [];
        const messages = window.EcosDB ? window.EcosDB.getMessages() : [];
        
        // Actualizar contadores
        updateElement('dashTotalBlogs', blogs.length);
        updateElement('dashTotalSubscribers', subscribers.length);
        updateElement('dashTotalMessages', messages.length);
        
        const totalViews = blogs.reduce((sum, blog) => sum + (blog.views || 0), 0);
        updateElement('dashTotalViews', totalViews);
        
        console.log('Datos del dashboard cargados:', {
            blogs: blogs.length,
            subscribers: subscribers.length,
            messages: messages.length,
            views: totalViews
        });
        
    } catch (error) {
        console.error('Error cargando datos del dashboard:', error);
    }
}

// Función auxiliar para actualizar elementos
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

// Configurar event listeners
function setupEventListeners() {
    // Logout button
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    // Botones de navegación
    const navButtons = document.querySelectorAll('[data-section]');
    navButtons.forEach(button => {
        button.addEventListener('click', function() {
            const section = this.getAttribute('data-section');
            showSection(section);
        });
    });
}

// Función para mostrar secciones
function showSection(sectionName) {
    console.log('Mostrando sección:', sectionName);
    
    // Ocultar todas las secciones
    const sections = document.querySelectorAll('.admin-section');
    sections.forEach(section => {
        section.classList.add('hidden');
    });
    
    // Mostrar la sección seleccionada
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.remove('hidden');
    }
    
    // Actualizar navegación activa
    const navItems = document.querySelectorAll('.sidebar-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });
    
    const activeItem = document.querySelector(`[data-section="${sectionName}"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
    
    // Cargar contenido específico
    switch(sectionName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'blogs':
            loadBlogsSection();
            break;
        case 'subscribers':
            loadSubscribersSection();
            break;
        case 'messages':
            loadMessagesSection();
            break;
    }
}

// Cargar sección de blogs
function loadBlogsSection() {
    console.log('Cargando sección de blogs...');
    
    const blogsList = document.getElementById('blogsList');
    if (!blogsList) return;
    
    const blogs = window.EcosDB ? window.EcosDB.getBlogs() : [];
    
    if (blogs.length === 0) {
        blogsList.innerHTML = `
            <div class="text-center py-12">
                <div class="text-6xl mb-4">📝</div>
                <h3 class="text-xl font-semibold mb-2 text-white">No hay blogs aún</h3>
                <p class="text-white/70 mb-4">Crea tu primer blog para comenzar</p>
                <button onclick="createNewBlog()" class="btn-magical px-6 py-3 rounded-xl">
                    Crear Primer Blog
                </button>
            </div>
        `;
        return;
    }
    
    blogsList.innerHTML = blogs.map(blog => `
        <div class="glass-enhanced rounded-xl p-6 hover:scale-105 transition-all duration-300">
            <div class="flex justify-between items-start mb-4">
                <div class="flex-1">
                    <h3 class="font-semibold text-lg mb-2 text-white">${blog.title}</h3>
                    <p class="text-white/70 text-sm mb-2">${blog.excerpt || 'Sin descripción'}</p>
                    <div class="flex items-center gap-4 text-sm text-white/60">
                        <span>📅 ${formatDate(blog.date)}</span>
                        <span>👁️ ${blog.views || 0} vistas</span>
                        <span class="px-2 py-1 rounded-full text-xs ${blog.published ? 'bg-green-500/20 text-green-300' : 'bg-yellow-500/20 text-yellow-300'}">
                            ${blog.published ? 'Publicado' : 'Borrador'}
                        </span>
                    </div>
                </div>
                <div class="flex gap-2 ml-4">
                    <button onclick="editBlog('${blog.id}')" class="btn-secondary px-3 py-1 text-sm rounded-lg">
                        Editar
                    </button>
                    <button onclick="toggleBlogStatus('${blog.id}')" class="btn-secondary px-3 py-1 text-sm rounded-lg">
                        ${blog.published ? 'Despublicar' : 'Publicar'}
                    </button>
                    <button onclick="deleteBlog('${blog.id}')" class="bg-red-500/20 text-red-300 hover:bg-red-500/30 px-3 py-1 text-sm rounded-lg transition-colors">
                        Eliminar
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// Cargar sección de suscriptores
function loadSubscribersSection() {
    console.log('Cargando sección de suscriptores...');
    
    const subscribersList = document.getElementById('subscribersList');
    if (!subscribersList) return;
    
    const subscribers = window.EcosDB ? window.EcosDB.getSubscribers() : [];
    
    if (subscribers.length === 0) {
        subscribersList.innerHTML = `
            <div class="text-center py-12">
                <div class="text-6xl mb-4">📧</div>
                <h3 class="text-xl font-semibold mb-2 text-white">No hay suscriptores aún</h3>
                <p class="text-white/70">Los suscriptores aparecerán aquí cuando se registren</p>
            </div>
        `;
        return;
    }
    
    subscribersList.innerHTML = subscribers.map(subscriber => `
        <div class="glass-enhanced rounded-xl p-4 flex justify-between items-center">
            <div>
                <h4 class="font-semibold text-white">${subscriber.name}</h4>
                <p class="text-white/70 text-sm">${subscriber.email}</p>
                <p class="text-white/50 text-xs">${formatDate(subscriber.date)}</p>
            </div>
            <button onclick="deleteSubscriber('${subscriber.id}')" class="bg-red-500/20 text-red-300 hover:bg-red-500/30 px-3 py-1 text-sm rounded-lg transition-colors">
                Eliminar
            </button>
        </div>
    `).join('');
}

// Cargar sección de mensajes
function loadMessagesSection() {
    console.log('Cargando sección de mensajes...');
    
    const messagesList = document.getElementById('messagesList');
    if (!messagesList) return;
    
    const messages = window.EcosDB ? window.EcosDB.getMessages() : [];
    
    if (messages.length === 0) {
        messagesList.innerHTML = `
            <div class="text-center py-12">
                <div class="text-6xl mb-4">💬</div>
                <h3 class="text-xl font-semibold mb-2 text-white">No hay mensajes aún</h3>
                <p class="text-white/70">Los mensajes de contacto aparecerán aquí</p>
            </div>
        `;
        return;
    }
    
    messagesList.innerHTML = messages.map(message => `
        <div class="glass-enhanced rounded-xl p-4">
            <div class="flex justify-between items-start mb-2">
                <h4 class="font-semibold text-white">${message.name}</h4>
                <span class="text-white/50 text-xs">${formatDate(message.date)}</span>
            </div>
            <p class="text-white/70 text-sm mb-2">${message.email}</p>
            <p class="text-white">${message.message}</p>
            <div class="mt-3 flex gap-2">
                <button onclick="deleteMessage('${message.id}')" class="bg-red-500/20 text-red-300 hover:bg-red-500/30 px-3 py-1 text-sm rounded-lg transition-colors">
                    Eliminar
                </button>
            </div>
        </div>
    `).join('');
}

// Funciones de acción
function createNewBlog() {
    window.location.href = 'editor.html';
}

function editBlog(blogId) {
    window.location.href = `editor.html?id=${blogId}`;
}

function toggleBlogStatus(blogId) {
    if (!window.EcosDB) return;
    
    const blogs = window.EcosDB.getBlogs();
    const blog = blogs.find(b => b.id === blogId);
    
    if (blog) {
        blog.published = !blog.published;
        window.EcosDB.saveBlog(blog);
        loadBlogsSection();
        showToast(`Blog ${blog.published ? 'publicado' : 'despublicado'} exitosamente`);
    }
}

function deleteBlog(blogId) {
    if (!window.EcosDB) return;
    
    const blogs = window.EcosDB.getBlogs();
    const blog = blogs.find(b => b.id === blogId);
    
    if (blog && confirm(`¿Estás seguro de que quieres eliminar "${blog.title}"?`)) {
        window.EcosDB.deleteBlog(blogId);
        loadBlogsSection();
        loadDashboardData();
        showToast('Blog eliminado exitosamente');
    }
}

function deleteSubscriber(subscriberId) {
    if (!window.EcosDB) return;
    
    if (confirm('¿Estás seguro de que quieres eliminar este suscriptor?')) {
        window.EcosDB.deleteSubscriber(subscriberId);
        loadSubscribersSection();
        loadDashboardData();
        showToast('Suscriptor eliminado exitosamente');
    }
}

function deleteMessage(messageId) {
    if (!window.EcosDB) return;
    
    if (confirm('¿Estás seguro de que quieres eliminar este mensaje?')) {
        window.EcosDB.deleteMessage(messageId);
        loadMessagesSection();
        loadDashboardData();
        showToast('Mensaje eliminado exitosamente');
    }
}

function handleLogout() {
    if (confirm('¿Estás seguro de que quieres cerrar sesión?')) {
        localStorage.removeItem('ecos_admin_session');
        window.location.href = 'admin-direct.html';
    }
}

// Funciones auxiliares
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('es-ES');
}

function showToast(message) {
    console.log('Toast:', message);
    if (window.EcosUtils && window.EcosUtils.toast) {
        window.EcosUtils.toast(message);
    } else {
        alert(message);
    }
}

// Hacer funciones disponibles globalmente
window.showSection = showSection;
window.createNewBlog = createNewBlog;
window.editBlog = editBlog;
window.toggleBlogStatus = toggleBlogStatus;
window.deleteBlog = deleteBlog;
window.deleteSubscriber = deleteSubscriber;
window.deleteMessage = deleteMessage;
window.logout = handleLogout;
