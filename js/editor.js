// JavaScript para el Editor de Blogs de Ecos del Alma

class BlogEditor {
    constructor() {
        this.currentBlogId = null;
        this.isEditing = false;
        this.autosaveInterval = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupAutosave();
        this.loadBlogFromURL();
        this.setupPlaceholder();
    }

    setupEventListeners() {
        // Botones principales
        const saveBtn = $('#saveBtn');
        const publishBtn = $('#publishBtn');
        const previewBtn = $('#previewBtn');

        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveBlog(false));
        }

        if (publishBtn) {
            publishBtn.addEventListener('click', () => this.saveBlog(true));
        }

        if (previewBtn) {
            previewBtn.addEventListener('click', () => this.showPreview());
        }

        // Editor de contenido
        const blogContent = $('#blogContent');
        if (blogContent) {
            blogContent.addEventListener('input', () => {
                this.updateWordCount();
                this.markAsModified();
            });

            blogContent.addEventListener('paste', (e) => {
                e.preventDefault();
                const text = e.clipboardData.getData('text/plain');
                document.execCommand('insertText', false, text);
            });
        }

        // Título
        const blogTitle = $('#blogTitle');
        if (blogTitle) {
            blogTitle.addEventListener('input', () => {
                this.markAsModified();
            });
        }

        // Otros campos
        ['#blogCategory', '#blogExcerpt', '#blogTags'].forEach(selector => {
            const element = $(selector);
            if (element) {
                element.addEventListener('input', () => this.markAsModified());
            }
        });

        // Atajos de teclado
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 's':
                        e.preventDefault();
                        this.saveBlog(false);
                        break;
                    case 'Enter':
                        e.preventDefault();
                        this.saveBlog(true);
                        break;
                    case 'p':
                        e.preventDefault();
                        this.showPreview();
                        break;
                }
            }
        });

        // Advertencia antes de salir
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    }

    setupAutosave() {
        this.autosaveInterval = setInterval(() => {
            if (this.hasUnsavedChanges()) {
                this.saveBlog(false, true); // Autoguardado silencioso
            }
        }, 30000); // Cada 30 segundos
    }

    setupPlaceholder() {
        const blogContent = $('#blogContent');
        if (blogContent) {
            const placeholder = blogContent.getAttribute('placeholder');
            
            blogContent.addEventListener('focus', () => {
                if (blogContent.textContent.trim() === '') {
                    blogContent.innerHTML = '';
                }
            });

            blogContent.addEventListener('blur', () => {
                if (blogContent.textContent.trim() === '') {
                    blogContent.innerHTML = `<span style="color: rgba(255,255,255,0.5); font-style: italic;">${placeholder}</span>`;
                }
            });

            // Inicializar placeholder
            if (blogContent.textContent.trim() === '') {
                blogContent.innerHTML = `<span style="color: rgba(255,255,255,0.5); font-style: italic;">${placeholder}</span>`;
            }
        }
    }

    loadBlogFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const blogId = urlParams.get('id');
        
        if (blogId) {
            this.loadBlog(blogId);
        }
    }

    loadBlog(blogId) {
        const blog = EcosDB.getBlog(blogId);
        if (!blog) {
            EcosUtils.toast('Blog no encontrado', 'error');
            return;
        }

        this.currentBlogId = blogId;
        this.isEditing = true;

        // Llenar formulario
        const blogTitle = $('#blogTitle');
        const blogCategory = $('#blogCategory');
        const blogExcerpt = $('#blogExcerpt');
        const blogTags = $('#blogTags');
        const blogContent = $('#blogContent');
        const blogFeatured = $('#blogFeatured');
        const blogPublished = $('#blogPublished');

        if (blogTitle) blogTitle.value = blog.title || '';
        if (blogCategory) blogCategory.value = blog.category || '';
        if (blogExcerpt) blogExcerpt.value = blog.excerpt || '';
        if (blogTags) blogTags.value = blog.tags ? blog.tags.join(', ') : '';
        if (blogContent) blogContent.innerHTML = blog.content || '';
        if (blogFeatured) blogFeatured.checked = blog.featured || false;
        if (blogPublished) blogPublished.checked = blog.published || false;

        // Actualizar título de la página
        document.title = `Editando: ${blog.title} — Ecos del Alma`;
        
        EcosUtils.toast('Blog cargado para edición');
    }

    saveBlog(publish = false, silent = false) {
        const formData = this.getFormData();
        
        if (!this.validateForm(formData)) {
            return;
        }

        const blog = {
            id: this.currentBlogId,
            title: formData.title,
            content: formData.content,
            excerpt: formData.excerpt,
            category: formData.category,
            tags: formData.tags,
            featured: formData.featured,
            published: publish || formData.published
        };

        try {
            const savedBlog = EcosDB.saveBlog(blog);
            this.currentBlogId = savedBlog.id;
            this.isEditing = true;
            
            if (!silent) {
                if (publish) {
                    EcosUtils.toast('¡Blog publicado exitosamente!');
                } else {
                    EcosUtils.toast('Blog guardado como borrador');
                }
            }

            // Actualizar URL si es un blog nuevo
            if (window.location.search === '') {
                const newUrl = `${window.location.pathname}?id=${savedBlog.id}`;
                window.history.replaceState({}, '', newUrl);
            }

            // Actualizar título de la página
            document.title = `Editando: ${blog.title} — Ecos del Alma`;
            
            this.markAsSaved();
            
        } catch (error) {
            console.error('Error al guardar blog:', error);
            EcosUtils.toast('Error al guardar el blog', 'error');
        }
    }

    getFormData() {
        const blogTitle = $('#blogTitle');
        const blogCategory = $('#blogCategory');
        const blogExcerpt = $('#blogExcerpt');
        const blogTags = $('#blogTags');
        const blogContent = $('#blogContent');
        const blogFeatured = $('#blogFeatured');
        const blogPublished = $('#blogPublished');

        // Limpiar contenido del placeholder
        let content = blogContent ? blogContent.innerHTML : '';
        if (content.includes('color: rgba(255,255,255,0.5)')) {
            content = '';
        }

        return {
            title: blogTitle ? blogTitle.value.trim() : '',
            category: blogCategory ? blogCategory.value : '',
            excerpt: blogExcerpt ? blogExcerpt.value.trim() : '',
            tags: blogTags ? blogTags.value.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
            content: content,
            featured: blogFeatured ? blogFeatured.checked : false,
            published: blogPublished ? blogPublished.checked : false
        };
    }

    validateForm(formData) {
        if (!formData.title) {
            EcosUtils.toast('El título es requerido', 'error');
            $('#blogTitle')?.focus();
            return false;
        }

        if (!formData.category) {
            EcosUtils.toast('La categoría es requerida', 'error');
            $('#blogCategory')?.focus();
            return false;
        }

        if (!formData.content || formData.content.trim() === '') {
            EcosUtils.toast('El contenido es requerido', 'error');
            $('#blogContent')?.focus();
            return false;
        }

        return true;
    }

    showPreview() {
        const formData = this.getFormData();
        
        if (!formData.title || !formData.content) {
            EcosUtils.toast('Completa al menos el título y contenido para ver la vista previa', 'warning');
            return;
        }

        const previewContent = $('#previewContent');
        if (!previewContent) return;

        const categoryNames = {
            'poesia': 'Poesía',
            'reflexiones': 'Reflexiones',
            'historias': 'Historias'
        };

        previewContent.innerHTML = `
            <div class="mb-6">
                <div class="flex items-center gap-3 mb-2">
                    <span class="glass text-sm px-3 py-1 rounded-full">${categoryNames[formData.category] || formData.category}</span>
                    ${formData.featured ? '<span class="text-sm px-3 py-1 rounded-full bg-softpink/20 text-softpink">Destacado</span>' : ''}
                    <span class="text-sm px-3 py-1 rounded-full ${formData.published ? 'bg-green-500/20 text-green-400' : 'bg-yellow-500/20 text-yellow-400'}">
                        ${formData.published ? 'Publicado' : 'Borrador'}
                    </span>
                </div>
                <h1 class="font-display text-3xl md:text-4xl mb-4">${EcosUtils.escapeHtml(formData.title)}</h1>
                ${formData.excerpt ? `<p class="text-white/80 text-lg mb-4">${EcosUtils.escapeHtml(formData.excerpt)}</p>` : ''}
                ${formData.tags.length > 0 ? `
                    <div class="flex flex-wrap gap-2 mb-6">
                        ${formData.tags.map(tag => `<span class="text-sm px-3 py-1 rounded-full bg-white/10">${EcosUtils.escapeHtml(tag)}</span>`).join('')}
                    </div>
                ` : ''}
            </div>
            <div class="prose prose-invert max-w-none">
                ${formData.content}
            </div>
        `;

        const previewModal = $('#previewModal');
        if (previewModal) {
            previewModal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }
    }

    closePreview() {
        const previewModal = $('#previewModal');
        if (previewModal) {
            previewModal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    }

    updateWordCount() {
        const blogContent = $('#blogContent');
        if (!blogContent) return;

        const text = blogContent.textContent || '';
        const wordCount = text.trim().split(/\s+/).filter(word => word.length > 0).length;
        
        // Actualizar contador si existe
        const counter = $('#wordCounter');
        if (counter) {
            counter.textContent = `${wordCount} palabras`;
        }
    }

    markAsModified() {
        const saveBtn = $('#saveBtn');
        if (saveBtn && !saveBtn.textContent.includes('*')) {
            saveBtn.textContent = 'Guardar *';
        }
    }

    markAsSaved() {
        const saveBtn = $('#saveBtn');
        if (saveBtn) {
            saveBtn.textContent = 'Guardar';
        }
    }

    hasUnsavedChanges() {
        const saveBtn = $('#saveBtn');
        return saveBtn && saveBtn.textContent.includes('*');
    }
}

// Funciones de formato de texto
window.formatText = (command) => {
    document.execCommand(command, false, null);
    $('#blogContent').focus();
};

window.insertQuote = () => {
    const selection = window.getSelection();
    const selectedText = selection.toString();
    
    if (selectedText) {
        document.execCommand('insertHTML', false, `<blockquote style="border-left: 3px solid #f472b6; padding-left: 1rem; margin: 1rem 0; font-style: italic;">${selectedText}</blockquote>`);
    } else {
        document.execCommand('insertHTML', false, `<blockquote style="border-left: 3px solid #f472b6; padding-left: 1rem; margin: 1rem 0; font-style: italic;">Escribe tu cita aquí...</blockquote>`);
    }
    $('#blogContent').focus();
};

window.insertDivider = () => {
    document.execCommand('insertHTML', false, '<hr style="border: none; border-top: 1px solid rgba(255,255,255,0.2); margin: 2rem 0;">');
    $('#blogContent').focus();
};

window.closePreview = () => {
    if (window.blogEditor) {
        blogEditor.closePreview();
    }
};

// Inicializar cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
    window.blogEditor = new BlogEditor();
});
