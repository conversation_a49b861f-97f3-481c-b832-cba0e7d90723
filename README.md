# Ecos del Alma - Blog de Poesía y Reflexiones

Un hermoso sitio web de blog completamente en español, diseñado para compartir poesía, reflexiones e historias gentiles. Con un diseño elegante en gradientes violeta y rosa, "Ecos del Alma" ofrece una experiencia completa de gestión de contenido.

## 🌟 Características Principales

### 📝 Gestión Completa de Blogs
- **Editor Visual**: Editor de texto enriquecido con herramientas de formato
- **Categorías**: Poesía, Reflexiones, Historias
- **Etiquetas**: Sistema de etiquetado flexible
- **Borradores y Publicación**: Guarda como borrador o publica inmediatamente
- **Blogs Destacados**: Marca blogs importantes como destacados

### 👥 Gestión de Audiencia
- **Suscriptores**: Sistema de suscripción por email
- **Mensajes de Contacto**: Formulario de contacto integrado
- **Gestión desde Admin**: Ve y gestiona suscriptores y mensajes

### 📊 Analytics Integrados
- **Visitantes del Sitio**: Rastrea visitas totales y únicas
- **Vistas por Blog**: Contador individual para cada blog
- **Países de Origen**: Estadísticas geográficas de visitantes
- **Gráficos Visuales**: Charts.js para visualización de datos

### 🎨 Diseño y UX
- **Diseño Responsivo**: Optimizado para móviles y desktop
- **Tema Elegante**: Gradientes violeta y rosa con efectos glass
- **Navegación Intuitiva**: Menús claros y fácil navegación
- **Búsqueda y Filtros**: Encuentra contenido fácilmente

## 🚀 Estructura del Proyecto

```
EcosDelAlma/
├── index.html              # Página principal
├── blogs.html              # Lista completa de blogs
├── admin.html              # Panel de administración
├── editor.html             # Editor de blogs
├── js/
│   ├── database.js         # Sistema de base de datos local
│   ├── utils.js            # Utilidades generales
│   ├── home.js             # JavaScript para página principal
│   ├── blogs.js            # JavaScript para página de blogs
│   ├── admin.js            # JavaScript para panel admin
│   └── editor.js           # JavaScript para editor
└── README.md               # Este archivo
```

## 🛠️ Tecnologías Utilizadas

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Estilos**: Tailwind CSS
- **Fuentes**: Google Fonts (Playfair Display, Nunito Sans)
- **Gráficos**: Chart.js
- **Almacenamiento**: localStorage (navegador)
- **Iconos**: SVG personalizados

## 📱 Páginas y Funcionalidades

### 🏠 Página Principal (`index.html`)
- Hero section con búsqueda
- Filtros por categoría
- Muestra de blogs destacados
- Formulario de suscripción
- Sección "Acerca del autor"
- Formulario de contacto

### 📚 Página de Blogs (`blogs.html`)
- Lista completa de todos los blogs
- Búsqueda avanzada
- Filtros por categoría
- Paginación
- Estadísticas de blogs
- Modal de lectura completa

### ⚙️ Panel de Administración (`admin.html`)
- **Dashboard**: Resumen general con estadísticas
- **Gestión de Blogs**: CRUD completo de blogs
- **Suscriptores**: Lista y gestión de suscriptores
- **Mensajes**: Bandeja de entrada de contactos
- **Analytics**: Gráficos y estadísticas detalladas
- **Configuración**: Ajustes del sitio y autor

### ✏️ Editor de Blogs (`editor.html`)
- Editor WYSIWYG con herramientas de formato
- Vista previa en tiempo real
- Autoguardado cada 30 segundos
- Gestión de categorías y etiquetas
- Opciones de publicación

## 🎯 Funcionalidades Destacadas

### 📊 Sistema de Analytics
- Rastrea visitantes únicos y totales
- Contabiliza vistas por blog individual
- Muestra países de origen de visitantes
- Gráficos de visitas diarias
- Top de blogs más populares

### 💾 Base de Datos Local
- Almacenamiento en localStorage del navegador
- Datos persistentes entre sesiones
- Estructura organizada para blogs, suscriptores, mensajes y analytics
- Inicialización automática con contenido de ejemplo

### 🔍 Búsqueda Inteligente
- Búsqueda en títulos, contenido y etiquetas
- Normalización de texto (sin acentos)
- Filtros por categoría
- Resultados en tiempo real

## 🎨 Diseño Visual

### Paleta de Colores
- **Lavender**: `#c4b5fd` (light), `#a78bfa` (default), `#7c3aed` (deep)
- **Soft Pink**: `#fbcfe8` (light), `#f472b6` (default), `#db2777` (deep)

### Efectos Visuales
- Gradientes de fondo dinámicos
- Efectos glass (cristal esmerilado)
- Sombras con glow
- Transiciones suaves
- Blobs decorativos difuminados

## 🚀 Cómo Usar

1. **Abrir el sitio**: Simplemente abre `index.html` en tu navegador
2. **Explorar blogs**: Navega por los blogs existentes
3. **Acceder al admin**: Ve a `admin.html` para gestionar contenido
4. **Crear blogs**: Usa el editor en `editor.html` o desde el panel admin
5. **Personalizar**: Modifica configuraciones desde el panel admin

## 📝 Gestión de Contenido

### Crear un Nuevo Blog
1. Ve al panel de administración
2. Haz clic en "Nuevo Blog" o ve directamente al editor
3. Completa título, categoría y contenido
4. Agrega etiquetas (opcional)
5. Marca como destacado si es necesario
6. Guarda como borrador o publica inmediatamente

### Gestionar Suscriptores
- Los usuarios se suscriben desde la página principal
- Ve la lista completa en el panel admin
- Elimina suscriptores si es necesario

### Ver Analytics
- Dashboard muestra resumen general
- Sección Analytics tiene detalles completos
- Gráficos de visitas diarias y países
- Lista de blogs más populares

## 🔧 Personalización

### Cambiar Información del Sitio
1. Ve al panel de administración
2. Accede a "Configuración"
3. Modifica nombre del sitio, descripción, información del autor
4. Guarda los cambios

### Agregar Nuevas Categorías
Modifica el archivo `js/database.js` y agrega nuevas categorías en las funciones correspondientes.

### Personalizar Estilos
Los estilos están definidos con Tailwind CSS. Puedes modificar la configuración en cada archivo HTML o agregar CSS personalizado.

## 🌐 Compatibilidad

- **Navegadores**: Chrome, Firefox, Safari, Edge (versiones modernas)
- **Dispositivos**: Desktop, tablet, móvil
- **Almacenamiento**: localStorage (datos persisten localmente)

## 📄 Licencia

Este proyecto es de código abierto y está disponible bajo la licencia MIT.

## 🤝 Contribuciones

Las contribuciones son bienvenidas. Por favor:
1. Fork el proyecto
2. Crea una rama para tu feature
3. Commit tus cambios
4. Push a la rama
5. Abre un Pull Request

## 📞 Soporte

Para soporte o preguntas, puedes:
- Abrir un issue en el repositorio
- Contactar a través del formulario de contacto del sitio

---

**Ecos del Alma** - Donde los susurros se vuelven palabras ✨
