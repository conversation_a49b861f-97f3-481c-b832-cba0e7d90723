<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Todos los Blogs — Ecos del Alma</title>
  <meta name="description" content="Explora todos los blogs de Ecos del Alma - poesía, reflexiones y historias gentiles que tocan el alma." />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            display: ['"Playfair Display"', 'serif'],
            body: ['"Nunito Sans"', 'ui-sans-serif', 'system-ui', 'sans-serif']
          },
          colors: {
            lavender: {
              light: '#c4b5fd',
              DEFAULT: '#a78bfa',
              deep: '#7c3aed'
            },
            softpink: {
              light: '#fbcfe8',
              DEFAULT: '#f472b6',
              deep: '#db2777'
            }
          },
          boxShadow: {
            glow: '0 10px 30px rgba(244, 114, 182, 0.25)'
          }
        }
      }
    }
  </script>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&family=Playfair+Display:wght@500;700&display=swap" rel="stylesheet"/>

  <style>
    ::selection { background: rgba(244,114,182,.35); color: #fff; }
    .bg-sheen {
      background: radial-gradient(1200px 600px at 80% -20%, rgba(255,255,255,.15), transparent 60%),
                  radial-gradient(1000px 500px at -10% 120%, rgba(244,114,182,.15), transparent 60%);
    }
    .glass {
      background: rgba(255,255,255,0.10);
      border: 1px solid rgba(255,255,255,0.18);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
    }
    .scroll-smooth { scroll-behavior: smooth; }
    .blog-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .blog-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 40px rgba(244, 114, 182, 0.3);
    }
    .filter-chip.active {
      background: rgba(244, 114, 182, 0.3);
      border-color: rgba(244, 114, 182, 0.5);
    }
  </style>
</head>
<body class="scroll-smooth font-body text-white min-h-screen relative overflow-x-hidden">
  <!-- Background gradient -->
  <div class="fixed inset-0 -z-20 bg-gradient-to-b from-lavender-light via-lavender to-lavender-deep"></div>
  <!-- Decorative blurred blobs -->
  <div class="pointer-events-none fixed -top-24 -left-24 w-[36rem] h-[36rem] rounded-full bg-softpink/30 blur-3xl -z-10"></div>
  <div class="pointer-events-none fixed top-1/4 -right-20 w-[28rem] h-[28rem] rounded-full bg-white/10 blur-2xl -z-10"></div>
  <div class="fixed inset-0 -z-10 bg-sheen"></div>

  <!-- Header / Nav -->
  <header class="sticky top-0 z-30">
    <nav class="glass mx-4 mt-4 rounded-2xl px-5 py-3 md:mx-auto md:max-w-6xl md:px-6 shadow-lg">
      <div class="flex items-center justify-between gap-4">
        <a href="index.html" class="group inline-flex items-center gap-3">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none" class="text-softpink">
            <path d="M20 3C14 3 8 9 8 9L4 13l7-1s6-6 9-9Z" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M4 13s1 3 4 4 6-1 6-1" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span class="font-display text-xl md:text-2xl tracking-wide">Ecos del Alma</span>
        </a>
        <div class="hidden md:flex items-center gap-3">
          <a href="index.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Inicio</a>
          <a href="blogs.html" class="px-3 py-2 rounded-xl bg-white/20 transition">Blogs</a>
          <a href="index.html#about" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Acerca</a>
          <a href="index.html#contact" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Contacto</a>
          <a href="admin.html" class="ml-1 inline-flex items-center gap-2 bg-softpink/20 hover:bg-softpink/30 text-white px-4 py-2 rounded-xl transition shadow-glow">
            <span>Admin</span>
          </a>
        </div>
        <!-- Mobile menu -->
        <div class="md:hidden flex items-center gap-2">
          <a href="index.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Inicio</a>
          <button onclick="toggleMobileMenu()" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Menú</button>
        </div>
      </div>
    </nav>
    <!-- Mobile dropdown -->
    <div id="mobileMenu" class="hidden mx-4 mt-2 glass rounded-2xl px-4 py-3 md:hidden">
      <div class="flex flex-col">
        <a href="index.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Inicio</a>
        <a href="blogs.html" class="px-3 py-2 rounded-xl bg-white/20 transition">Blogs</a>
        <a href="index.html#about" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Acerca</a>
        <a href="index.html#contact" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Contacto</a>
        <a href="admin.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Admin</a>
      </div>
    </div>
  </header>

  <!-- Hero Section -->
  <section class="mt-8 md:mt-12 px-4 md:px-6">
    <div class="max-w-6xl mx-auto">
      <div class="glass rounded-3xl p-7 md:p-10 shadow-xl text-center">
        <h1 class="font-display text-4xl md:text-5xl leading-tight mb-4">
          Todos los Blogs
        </h1>
        <p class="text-white/85 text-lg md:text-xl mb-6">
          Explora la colección completa de susurros, reflexiones y poemas que habitan en este rincón del alma.
        </p>
        
        <!-- Search and Filters -->
        <div class="max-w-2xl mx-auto space-y-4">
          <div class="flex items-center gap-3 glass rounded-2xl px-4 py-3 focus-within:ring-2 focus-within:ring-softpink">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" class="opacity-80">
              <circle cx="11" cy="11" r="7" stroke="white" stroke-width="1.5" />
              <path d="M20 20l-3.2-3.2" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            <input id="searchInput" type="text" placeholder="Buscar por título, contenido o etiquetas..." class="w-full bg-transparent outline-none placeholder-white/60"/>
            <button id="clearSearch" type="button" class="hidden text-softpink hover:text-softpink-deep transition">Limpiar</button>
          </div>
          
          <!-- Category filters -->
          <div class="flex flex-wrap justify-center gap-2" id="filters">
            <button data-filter="all" class="filter-chip active glass px-4 py-2 rounded-xl hover:bg-white/20 transition">Todos</button>
            <button data-filter="poesia" class="filter-chip glass px-4 py-2 rounded-xl hover:bg-white/20 transition">Poesía</button>
            <button data-filter="reflexiones" class="filter-chip glass px-4 py-2 rounded-xl hover:bg-white/20 transition">Reflexiones</button>
            <button data-filter="historias" class="filter-chip glass px-4 py-2 rounded-xl hover:bg-white/20 transition">Historias</button>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <section class="px-4 md:px-6 mt-8">
    <div class="max-w-6xl mx-auto">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="glass rounded-2xl p-4 text-center">
          <div class="text-2xl font-bold text-softpink" id="totalBlogs">0</div>
          <div class="text-sm text-white/70">Blogs</div>
        </div>
        <div class="glass rounded-2xl p-4 text-center">
          <div class="text-2xl font-bold text-softpink" id="totalViews">0</div>
          <div class="text-sm text-white/70">Vistas</div>
        </div>
        <div class="glass rounded-2xl p-4 text-center">
          <div class="text-2xl font-bold text-softpink" id="totalPoetry">0</div>
          <div class="text-sm text-white/70">Poemas</div>
        </div>
        <div class="glass rounded-2xl p-4 text-center">
          <div class="text-2xl font-bold text-softpink" id="totalReflections">0</div>
          <div class="text-sm text-white/70">Reflexiones</div>
        </div>
      </div>
    </div>
  </section>

  <!-- Blogs Grid -->
  <section class="px-4 md:px-6 mt-8 mb-16">
    <div class="max-w-6xl mx-auto">
      <div class="flex items-center justify-between mb-6">
        <h2 class="font-display text-2xl md:text-3xl">
          <span id="resultsCount">Cargando...</span>
        </h2>
        <div class="flex items-center gap-2">
          <label for="sortBy" class="text-sm text-white/70">Ordenar por:</label>
          <select id="sortBy" class="glass rounded-xl px-3 py-2 bg-transparent text-white text-sm">
            <option value="date">Fecha</option>
            <option value="views">Vistas</option>
            <option value="title">Título</option>
          </select>
        </div>
      </div>

      <div id="blogsGrid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Los blogs se cargarán dinámicamente aquí -->
      </div>

      <!-- Pagination -->
      <div id="pagination" class="flex justify-center mt-8 gap-2">
        <!-- Los botones de paginación se generarán dinámicamente -->
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="px-4 md:px-6 pb-10">
    <div class="max-w-6xl mx-auto glass rounded-3xl p-6 flex flex-col sm:flex-row items-center justify-between">
      <p class="text-white/80">&copy; <span id="year"></span> Ecos del Alma</p>
      <div class="flex items-center gap-2 text-white/70 text-sm mt-2 sm:mt-0">
        <span>Hecho con calma y curiosidad.</span>
      </div>
    </div>
  </footer>

  <!-- Blog Modal -->
  <div id="blogModal" class="fixed inset-0 z-40 hidden">
    <div class="absolute inset-0 bg-black/50" onclick="closeBlogModal()"></div>
    <div class="relative max-w-4xl mx-auto mt-8 md:mt-16 px-4 max-h-[90vh] overflow-y-auto">
      <div class="glass rounded-3xl p-6 md:p-8">
        <div class="flex items-start justify-between gap-4 mb-6">
          <div class="flex-1">
            <h3 id="modalTitle" class="font-display text-2xl md:text-3xl mb-2"></h3>
            <div class="flex items-center gap-4 text-white/70 text-sm">
              <span id="modalDate"></span>
              <span id="modalViews"></span>
              <span id="modalCategory"></span>
            </div>
          </div>
          <button onclick="closeBlogModal()" class="rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Cerrar">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
              <path d="M6 6l12 12M18 6 6 18" stroke="white" stroke-width="1.8" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        
        <div id="modalTags" class="flex flex-wrap gap-2 mb-6">
          <!-- Tags se cargarán aquí -->
        </div>
        
        <div id="modalContent" class="prose prose-invert max-w-none">
          <!-- Contenido del blog se cargará aquí -->
        </div>
        
        <div class="mt-8 flex items-center justify-between">
          <button id="modalPrev" class="rounded-xl px-4 py-2 glass hover:bg-white/20 transition">← Anterior</button>
          <button onclick="shareBlog()" class="rounded-xl px-4 py-2 glass hover:bg-white/20 transition">Compartir</button>
          <button id="modalNext" class="rounded-xl px-4 py-2 glass hover:bg-white/20 transition">Siguiente →</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast -->
  <div id="toast" class="fixed bottom-6 left-1/2 -translate-x-1/2 z-50 hidden">
    <div class="glass rounded-xl px-4 py-3 flex items-center gap-2 bg-softpink/25">
      <span id="toastMsg">¡Hecho!</span>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/database.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/blogs.js"></script>
</body>
</html>
