<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Panel de Administración — Ecos del Alma</title>
  <meta name="description" content="Panel de administración para gestionar el contenido de Ecos del Alma" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            display: ['"Playfair Display"', 'serif'],
            body: ['"Nunito Sans"', 'ui-sans-serif', 'system-ui', 'sans-serif']
          },
          colors: {
            lavender: {
              light: '#c4b5fd',
              DEFAULT: '#a78bfa',
              deep: '#7c3aed'
            },
            softpink: {
              light: '#fbcfe8',
              DEFAULT: '#f472b6',
              deep: '#db2777'
            }
          },
          boxShadow: {
            glow: '0 10px 30px rgba(244, 114, 182, 0.25)'
          }
        }
      }
    }
  </script>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&family=Playfair+Display:wght@500;700&display=swap" rel="stylesheet"/>

  <!-- Chart.js para analytics -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

  <style>
    ::selection { background: rgba(244,114,182,.35); color: #fff; }
    .bg-sheen {
      background: radial-gradient(1200px 600px at 80% -20%, rgba(255,255,255,.15), transparent 60%),
                  radial-gradient(1000px 500px at -10% 120%, rgba(244,114,182,.15), transparent 60%);
    }
    .glass {
      background: rgba(255,255,255,0.10);
      border: 1px solid rgba(255,255,255,0.18);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
    }
    .scroll-smooth { scroll-behavior: smooth; }
    .sidebar-item {
      transition: all 0.3s ease;
    }
    .sidebar-item:hover {
      background: rgba(255,255,255,0.15);
      transform: translateX(4px);
    }
    .sidebar-item.active {
      background: rgba(244, 114, 182, 0.3);
      border-left: 3px solid #f472b6;
    }
    .admin-card {
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .admin-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 15px 30px rgba(244, 114, 182, 0.2);
    }
    .editor-toolbar {
      border-bottom: 1px solid rgba(255,255,255,0.1);
    }
    .editor-content {
      min-height: 300px;
      max-height: 500px;
      overflow-y: auto;
    }
  </style>
</head>
<body class="scroll-smooth font-body text-white min-h-screen relative overflow-x-hidden">
  <!-- Background gradient -->
  <div class="fixed inset-0 -z-20 bg-gradient-to-b from-lavender-light via-lavender to-lavender-deep"></div>
  <!-- Decorative blurred blobs -->
  <div class="pointer-events-none fixed -top-24 -left-24 w-[36rem] h-[36rem] rounded-full bg-softpink/30 blur-3xl -z-10"></div>
  <div class="pointer-events-none fixed top-1/4 -right-20 w-[28rem] h-[28rem] rounded-full bg-white/10 blur-2xl -z-10"></div>
  <div class="fixed inset-0 -z-10 bg-sheen"></div>

  <!-- Header -->
  <header class="sticky top-0 z-30">
    <nav class="glass mx-4 mt-4 rounded-2xl px-5 py-3 md:mx-auto md:max-w-7xl md:px-6 shadow-lg">
      <div class="flex items-center justify-between gap-4">
        <a href="index.html" class="group inline-flex items-center gap-3">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none" class="text-softpink">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span class="font-display text-xl md:text-2xl tracking-wide">Panel de Administración</span>
        </a>
        <div class="flex items-center gap-3">
          <a href="index.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Ver Sitio</a>
          <a href="blogs.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Ver Blogs</a>
          <button onclick="showNotifications()" class="relative px-3 py-2 rounded-xl hover:bg-white/10 transition">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9" stroke="currentColor" stroke-width="1.5"/>
              <path d="M13.73 21a2 2 0 0 1-3.46 0" stroke="currentColor" stroke-width="1.5"/>
            </svg>
            <span id="notificationBadge" class="absolute -top-1 -right-1 bg-softpink text-xs rounded-full w-5 h-5 flex items-center justify-center hidden">0</span>
          </button>
          <button onclick="logout()" class="px-3 py-2 rounded-xl bg-red-500/20 hover:bg-red-500/30 text-red-200 transition">
            Cerrar Sesión
          </button>
        </div>
      </div>
    </nav>
  </header>

  <div class="flex min-h-screen">
    <!-- Sidebar -->
    <aside class="w-64 p-4 hidden lg:block">
      <div class="glass rounded-2xl p-4 sticky top-24">
        <nav class="space-y-2">
          <button onclick="showSection('dashboard')" class="sidebar-item active w-full text-left px-4 py-3 rounded-xl flex items-center gap-3">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="1.5"/>
              <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="1.5"/>
              <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="1.5"/>
              <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="1.5"/>
            </svg>
            <span>Dashboard</span>
          </button>
          
          <button onclick="showSection('blogs')" class="sidebar-item w-full text-left px-4 py-3 rounded-xl flex items-center gap-3">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="1.5"/>
              <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="1.5"/>
              <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="1.5"/>
              <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="1.5"/>
            </svg>
            <span>Gestionar Blogs</span>
          </button>
          
          <button onclick="showSection('subscribers')" class="sidebar-item w-full text-left px-4 py-3 rounded-xl flex items-center gap-3">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="1.5"/>
              <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="1.5"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="currentColor" stroke-width="1.5"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="currentColor" stroke-width="1.5"/>
            </svg>
            <span>Suscriptores</span>
          </button>
          
          <button onclick="showSection('messages')" class="sidebar-item w-full text-left px-4 py-3 rounded-xl flex items-center gap-3">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="1.5"/>
              <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="1.5"/>
            </svg>
            <span>Mensajes</span>
            <span id="messagesBadge" class="bg-softpink text-xs rounded-full px-2 py-1 ml-auto hidden">0</span>
          </button>
          
          <button onclick="showSection('analytics')" class="sidebar-item w-full text-left px-4 py-3 rounded-xl flex items-center gap-3">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="1.5"/>
            </svg>
            <span>Analytics</span>
          </button>
          
          <button onclick="showSection('settings')" class="sidebar-item w-full text-left px-4 py-3 rounded-xl flex items-center gap-3">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5"/>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" stroke="currentColor" stroke-width="1.5"/>
            </svg>
            <span>Configuración</span>
          </button>
        </nav>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="flex-1 p-4 lg:p-6">
      <!-- Dashboard Section -->
      <section id="dashboard-section" class="admin-section">
        <div class="mb-6">
          <h1 class="font-display text-3xl md:text-4xl mb-2">Dashboard</h1>
          <p class="text-white/70">Resumen general de tu blog Ecos del Alma</p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div class="admin-card glass rounded-2xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-white/70 text-sm">Total Blogs</p>
                <p class="text-2xl font-bold text-softpink" id="dashTotalBlogs">0</p>
              </div>
              <div class="w-12 h-12 bg-softpink/20 rounded-xl flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-softpink">
                  <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" stroke="currentColor" stroke-width="1.5"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="admin-card glass rounded-2xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-white/70 text-sm">Total Vistas</p>
                <p class="text-2xl font-bold text-softpink" id="dashTotalViews">0</p>
              </div>
              <div class="w-12 h-12 bg-softpink/20 rounded-xl flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-softpink">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="1.5"/>
                  <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="admin-card glass rounded-2xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-white/70 text-sm">Suscriptores</p>
                <p class="text-2xl font-bold text-softpink" id="dashTotalSubscribers">0</p>
              </div>
              <div class="w-12 h-12 bg-softpink/20 rounded-xl flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-softpink">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="1.5"/>
                  <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="1.5"/>
                </svg>
              </div>
            </div>
          </div>

          <div class="admin-card glass rounded-2xl p-6">
            <div class="flex items-center justify-between">
              <div>
                <p class="text-white/70 text-sm">Mensajes</p>
                <p class="text-2xl font-bold text-softpink" id="dashTotalMessages">0</p>
              </div>
              <div class="w-12 h-12 bg-softpink/20 rounded-xl flex items-center justify-center">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" class="text-softpink">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="1.5"/>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <div class="glass rounded-2xl p-6">
            <h3 class="font-display text-xl mb-4">Acciones Rápidas</h3>
            <div class="space-y-3">
              <button onclick="showSection('blogs'); openBlogEditor()" class="w-full text-left px-4 py-3 rounded-xl bg-softpink hover:bg-softpink-deep transition flex items-center gap-3">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M12 5v14M5 12h14" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
                <span>Crear Nuevo Blog</span>
              </button>
              <button onclick="showSection('messages')" class="w-full text-left px-4 py-3 rounded-xl glass hover:bg-white/20 transition flex items-center gap-3">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="1.5"/>
                </svg>
                <span>Ver Mensajes</span>
              </button>
              <button onclick="showSection('analytics')" class="w-full text-left px-4 py-3 rounded-xl glass hover:bg-white/20 transition flex items-center gap-3">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                  <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" stroke="currentColor" stroke-width="1.5"/>
                </svg>
                <span>Ver Analytics</span>
              </button>
            </div>
          </div>

          <div class="glass rounded-2xl p-6">
            <h3 class="font-display text-xl mb-4">Blogs Recientes</h3>
            <div id="recentBlogs" class="space-y-3">
              <!-- Se llenarán dinámicamente -->
            </div>
          </div>
        </div>

        <!-- Charts -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="glass rounded-2xl p-6">
            <h3 class="font-display text-xl mb-4">Visitas Diarias</h3>
            <canvas id="dailyVisitsChart" width="400" height="200"></canvas>
          </div>
          <div class="glass rounded-2xl p-6">
            <h3 class="font-display text-xl mb-4">Visitantes por País</h3>
            <canvas id="countriesChart" width="400" height="200"></canvas>
          </div>
        </div>
      </section>

      <!-- Other sections will be added here -->
      <section id="blogs-section" class="admin-section hidden">
        <div class="mb-6">
          <div class="flex items-center justify-between">
            <div>
              <h1 class="font-display text-3xl md:text-4xl mb-2">Gestionar Blogs</h1>
              <p class="text-white/70">Crea, edita y gestiona tus blogs</p>
            </div>
            <button onclick="openBlogEditor()" class="inline-flex items-center gap-2 bg-softpink hover:bg-softpink-deep px-4 py-2 rounded-xl transition shadow-glow">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 5v14M5 12h14" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
              </svg>
              <span>Nuevo Blog</span>
            </button>
          </div>
        </div>

        <!-- Blogs List -->
        <div id="blogsList" class="space-y-4">
          <!-- Se llenarán dinámicamente -->
        </div>
      </section>

      <!-- Placeholder sections for other features -->
      <section id="subscribers-section" class="admin-section hidden">
        <h1 class="font-display text-3xl md:text-4xl mb-6">Suscriptores</h1>
        <div id="subscribersList"></div>
      </section>

      <section id="messages-section" class="admin-section hidden">
        <h1 class="font-display text-3xl md:text-4xl mb-6">Mensajes</h1>
        <div id="messagesList"></div>
      </section>

      <section id="analytics-section" class="admin-section hidden">
        <h1 class="font-display text-3xl md:text-4xl mb-6">Analytics</h1>
        <div id="analyticsContent"></div>
      </section>

      <section id="settings-section" class="admin-section hidden">
        <h1 class="font-display text-3xl md:text-4xl mb-6">Configuración</h1>
        <div id="settingsContent"></div>
      </section>
    </main>
  </div>

  <!-- Mobile Menu Button (for smaller screens) -->
  <button id="mobileMenuBtn" class="lg:hidden fixed bottom-6 right-6 z-40 bg-softpink rounded-full p-3 shadow-glow">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <line x1="3" y1="6" x2="21" y2="6" stroke="white" stroke-width="2"/>
      <line x1="3" y1="12" x2="21" y2="12" stroke="white" stroke-width="2"/>
      <line x1="3" y1="18" x2="21" y2="18" stroke="white" stroke-width="2"/>
    </svg>
  </button>

  <!-- Toast -->
  <div id="toast" class="fixed bottom-6 left-1/2 -translate-x-1/2 z-50 hidden">
    <div class="glass rounded-xl px-4 py-3 flex items-center gap-2 bg-softpink/25">
      <span id="toastMsg">¡Hecho!</span>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/auth.js"></script>
  <script src="js/database.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/admin.js"></script>

  <script>
    // Verificar autenticación de forma simple
    document.addEventListener('DOMContentLoaded', () => {
      // Verificar si hay sesión válida
      const session = localStorage.getItem('ecos_admin_session');
      let isAuthenticated = false;

      if (session) {
        try {
          const sessionData = JSON.parse(session);
          const now = new Date().getTime();
          if (sessionData.expires > now) {
            isAuthenticated = true;
          }
        } catch (error) {
          // Sesión inválida
        }
      }

      // Si no está autenticado, redirigir al login
      if (!isAuthenticated) {
        window.location.href = 'admin/index.html';
        return;
      }

      // Si llegamos aquí, el usuario está autenticado
      console.log('Usuario autenticado, cargando panel admin...');
    });
  </script>
</body>
</html>
