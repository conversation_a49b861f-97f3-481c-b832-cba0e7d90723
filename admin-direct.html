<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Acceso Directo Admin — Ecos del Alma</title>
  <meta name="description" content="Panel de administración para Ecos del Alma" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            display: ['"Playfair Display"', 'serif'],
            body: ['"Nunito Sans"', 'ui-sans-serif', 'system-ui', 'sans-serif']
          },
          colors: {
            lavender: {
              light: '#c4b5fd',
              DEFAULT: '#a78bfa',
              deep: '#7c3aed'
            },
            softpink: {
              light: '#fbcfe8',
              DEFAULT: '#f472b6',
              deep: '#db2777'
            }
          }
        }
      }
    }
  </script>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&family=Playfair+Display:wght@500;700&display=swap" rel="stylesheet"/>

  <!-- Magical CSS -->
  <link rel="stylesheet" href="css/magical.css">

  <style>
    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  </style>
</head>
<body class="font-body text-white overflow-hidden">
  <!-- Magical Background -->
  <div class="magical-background"></div>
  
  <!-- Particles -->
  <div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
  </div>

  <!-- Login Container -->
  <div class="login-container px-4">
    <div class="glass-enhanced rounded-3xl p-8 md:p-12 w-full max-w-md mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="mb-4">
          <svg width="60" height="60" viewBox="0 0 24 24" fill="none" class="mx-auto text-softpink">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 class="font-display text-3xl md:text-4xl mb-2 text-gradient">Ecos del Alma</h1>
        <p class="text-white/70">Acceso Directo al Admin</p>
      </div>

      <!-- Quick Access Form -->
      <form id="quickAccessForm" class="space-y-6">
        <div>
          <label for="password" class="block text-sm font-medium mb-2 text-white/90">
            Contraseña de Acceso
          </label>
          <input 
            type="password" 
            id="password" 
            name="password" 
            required
            class="w-full glass-enhanced rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink text-white placeholder-white/50"
            placeholder="EcosDelAlma2024!"
          />
        </div>

        <!-- Submit Button -->
        <button 
          type="submit" 
          class="w-full btn-magical rounded-xl px-6 py-3 font-semibold text-white transition-all duration-300"
        >
          Acceder al Admin
        </button>
      </form>

      <!-- Quick Access Buttons -->
      <div class="mt-6 space-y-3">
        <button 
          onclick="quickAccess()" 
          class="w-full btn-secondary rounded-xl px-6 py-3 font-semibold text-white transition-all duration-300"
        >
          Acceso Rápido (Sin Contraseña)
        </button>
        
        <div class="text-center">
          <a href="index.html" class="text-softpink hover:text-softpink-light transition-colors text-sm">
            ← Volver al sitio principal
          </a>
        </div>
      </div>

      <!-- Info -->
      <div class="mt-6 p-4 glass-enhanced rounded-xl">
        <div class="text-xs text-white/60">
          <p class="font-medium mb-2">Acceso Temporal:</p>
          <ul class="space-y-1">
            <li>• Contraseña por defecto: <code class="bg-white/10 px-1 rounded">EcosDelAlma2024!</code></li>
            <li>• O usa "Acceso Rápido" para entrar directamente</li>
            <li>• Esta página es temporal para solucionar el problema</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script>
    // Función de acceso rápido
    function quickAccess() {
      // Crear sesión directamente
      const now = new Date().getTime();
      const sessionData = {
        authenticated: true,
        timestamp: now,
        expires: now + (24 * 60 * 60 * 1000) // 24 horas
      };
      
      localStorage.setItem('ecos_admin_session', JSON.stringify(sessionData));
      
      // Redirigir al admin
      window.location.href = 'admin.html';
    }

    // Manejar formulario
    document.getElementById('quickAccessForm').addEventListener('submit', function(e) {
      e.preventDefault();
      
      const password = document.getElementById('password').value;
      const correctPassword = 'EcosDelAlma2024!';
      
      if (password === correctPassword) {
        // Crear sesión
        const now = new Date().getTime();
        const sessionData = {
          authenticated: true,
          timestamp: now,
          expires: now + (24 * 60 * 60 * 1000) // 24 horas
        };
        
        localStorage.setItem('ecos_admin_session', JSON.stringify(sessionData));
        
        // Redirigir al admin
        window.location.href = 'admin.html';
      } else {
        alert('Contraseña incorrecta. Usa: EcosDelAlma2024!');
      }
    });

    // Auto-focus
    document.addEventListener('DOMContentLoaded', () => {
      document.getElementById('password').focus();
    });

    // Verificar si ya está autenticado
    document.addEventListener('DOMContentLoaded', () => {
      const session = localStorage.getItem('ecos_admin_session');
      if (session) {
        try {
          const sessionData = JSON.parse(session);
          const now = new Date().getTime();
          if (sessionData.expires > now) {
            // Ya está autenticado, redirigir
            window.location.href = 'admin.html';
          }
        } catch (error) {
          // Sesión inválida, continuar
        }
      }
    });
  </script>
</body>
</html>
