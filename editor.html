<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Editor de Blogs — Ecos del Alma</title>
  <meta name="description" content="Editor para crear y editar blogs en Ecos del Alma" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            display: ['"Playfair Display"', 'serif'],
            body: ['"Nunito Sans"', 'ui-sans-serif', 'system-ui', 'sans-serif']
          },
          colors: {
            lavender: {
              light: '#c4b5fd',
              DEFAULT: '#a78bfa',
              deep: '#7c3aed'
            },
            softpink: {
              light: '#fbcfe8',
              DEFAULT: '#f472b6',
              deep: '#db2777'
            }
          },
          boxShadow: {
            glow: '0 10px 30px rgba(244, 114, 182, 0.25)'
          }
        }
      }
    }
  </script>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&family=Playfair+Display:wght@500;700&display=swap" rel="stylesheet"/>

  <style>
    ::selection { background: rgba(244,114,182,.35); color: #fff; }
    .bg-sheen {
      background: radial-gradient(1200px 600px at 80% -20%, rgba(255,255,255,.15), transparent 60%),
                  radial-gradient(1000px 500px at -10% 120%, rgba(244,114,182,.15), transparent 60%);
    }
    .glass {
      background: rgba(255,255,255,0.10);
      border: 1px solid rgba(255,255,255,0.18);
      backdrop-filter: blur(12px);
      -webkit-backdrop-filter: blur(12px);
    }
    .scroll-smooth { scroll-behavior: smooth; }
    .editor-content {
      min-height: 400px;
      max-height: 600px;
      overflow-y: auto;
    }
    .editor-content:focus {
      outline: none;
      ring: 2px solid #f472b6;
    }
    .toolbar-button {
      transition: all 0.2s ease;
    }
    .toolbar-button:hover {
      background: rgba(255,255,255,0.2);
      transform: translateY(-1px);
    }
    .toolbar-button.active {
      background: rgba(244, 114, 182, 0.3);
    }
  </style>
</head>
<body class="scroll-smooth font-body text-white min-h-screen relative overflow-x-hidden">
  <!-- Background gradient -->
  <div class="fixed inset-0 -z-20 bg-gradient-to-b from-lavender-light via-lavender to-lavender-deep"></div>
  <!-- Decorative blurred blobs -->
  <div class="pointer-events-none fixed -top-24 -left-24 w-[36rem] h-[36rem] rounded-full bg-softpink/30 blur-3xl -z-10"></div>
  <div class="pointer-events-none fixed top-1/4 -right-20 w-[28rem] h-[28rem] rounded-full bg-white/10 blur-2xl -z-10"></div>
  <div class="fixed inset-0 -z-10 bg-sheen"></div>

  <!-- Header -->
  <header class="sticky top-0 z-30">
    <nav class="glass mx-4 mt-4 rounded-2xl px-5 py-3 md:mx-auto md:max-w-7xl md:px-6 shadow-lg">
      <div class="flex items-center justify-between gap-4">
        <a href="admin.html" class="group inline-flex items-center gap-3">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none" class="text-softpink">
            <path d="M19 12H5M12 19l-7-7 7-7" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span class="font-display text-xl md:text-2xl tracking-wide">Editor de Blogs</span>
        </a>
        <div class="flex items-center gap-3">
          <button id="previewBtn" class="px-4 py-2 rounded-xl glass hover:bg-white/20 transition">Vista Previa</button>
          <button id="saveBtn" class="px-4 py-2 rounded-xl bg-softpink hover:bg-softpink-deep transition shadow-glow">Guardar</button>
          <button id="publishBtn" class="px-4 py-2 rounded-xl bg-green-500 hover:bg-green-600 transition shadow-glow">Publicar</button>
        </div>
      </div>
    </nav>
  </header>

  <div class="max-w-6xl mx-auto p-4 lg:p-6">
    <!-- Blog Form -->
    <form id="blogForm" class="space-y-6">
      <!-- Basic Info -->
      <div class="glass rounded-2xl p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium mb-2">Título del Blog</label>
            <input type="text" id="blogTitle" name="title" required
                   class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink"
                   placeholder="Escribe un título hermoso...">
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Categoría</label>
            <select id="blogCategory" name="category" required
                    class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink">
              <option value="">Selecciona una categoría</option>
              <option value="poesia">Poesía</option>
              <option value="reflexiones">Reflexiones</option>
              <option value="historias">Historias</option>
            </select>
          </div>
          <div class="lg:col-span-2">
            <label class="block text-sm font-medium mb-2">Extracto (Opcional)</label>
            <textarea id="blogExcerpt" name="excerpt" rows="2"
                      class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink"
                      placeholder="Un breve resumen que aparecerá en las tarjetas de blog..."></textarea>
          </div>
          <div class="lg:col-span-2">
            <label class="block text-sm font-medium mb-2">Etiquetas (separadas por comas)</label>
            <input type="text" id="blogTags" name="tags"
                   class="w-full glass rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink"
                   placeholder="poesía, amor, naturaleza, reflexión...">
          </div>
        </div>
      </div>

      <!-- Content Editor -->
      <div class="glass rounded-2xl overflow-hidden">
        <div class="p-4 border-b border-white/10">
          <div class="flex items-center gap-2 flex-wrap">
            <button type="button" class="toolbar-button p-2 rounded-lg" onclick="formatText('bold')" title="Negrita">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z" stroke="currentColor" stroke-width="1.5"/>
                <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </button>
            <button type="button" class="toolbar-button p-2 rounded-lg" onclick="formatText('italic')" title="Cursiva">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <line x1="19" y1="4" x2="10" y2="4" stroke="currentColor" stroke-width="1.5"/>
                <line x1="14" y1="20" x2="5" y2="20" stroke="currentColor" stroke-width="1.5"/>
                <line x1="15" y1="4" x2="9" y2="20" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </button>
            <div class="w-px h-6 bg-white/20"></div>
            <button type="button" class="toolbar-button p-2 rounded-lg" onclick="formatText('insertUnorderedList')" title="Lista">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="1.5"/>
                <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="1.5"/>
                <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="1.5"/>
                <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="1.5"/>
                <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="1.5"/>
                <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </button>
            <button type="button" class="toolbar-button p-2 rounded-lg" onclick="formatText('insertOrderedList')" title="Lista numerada">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <line x1="10" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="1.5"/>
                <line x1="10" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="1.5"/>
                <line x1="10" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="1.5"/>
                <path d="M4 6h1v4" stroke="currentColor" stroke-width="1.5"/>
                <path d="M4 10h2" stroke="currentColor" stroke-width="1.5"/>
                <path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </button>
            <div class="w-px h-6 bg-white/20"></div>
            <button type="button" class="toolbar-button p-2 rounded-lg" onclick="insertQuote()" title="Cita">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" stroke="currentColor" stroke-width="1.5"/>
                <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </button>
            <button type="button" class="toolbar-button p-2 rounded-lg" onclick="insertDivider()" title="Separador">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <line x1="3" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </button>
          </div>
        </div>
        <div class="p-6">
          <div id="blogContent" 
               contenteditable="true" 
               class="editor-content w-full bg-transparent outline-none text-white/90 leading-relaxed"
               placeholder="Comienza a escribir tu blog aquí... Deja que las palabras fluyan como susurros del alma.">
          </div>
        </div>
      </div>

      <!-- Options -->
      <div class="glass rounded-2xl p-6">
        <h3 class="font-display text-xl mb-4">Opciones de Publicación</h3>
        <div class="flex flex-wrap gap-4">
          <label class="flex items-center gap-2">
            <input type="checkbox" id="blogFeatured" name="featured" class="rounded">
            <span>Blog destacado</span>
          </label>
          <label class="flex items-center gap-2">
            <input type="checkbox" id="blogPublished" name="published" class="rounded">
            <span>Publicar inmediatamente</span>
          </label>
        </div>
      </div>
    </form>
  </div>

  <!-- Preview Modal -->
  <div id="previewModal" class="fixed inset-0 z-50 hidden">
    <div class="absolute inset-0 bg-black/50" onclick="closePreview()"></div>
    <div class="relative max-w-4xl mx-auto mt-8 md:mt-16 px-4 max-h-[90vh] overflow-y-auto">
      <div class="glass rounded-3xl p-6 md:p-8">
        <div class="flex items-start justify-between gap-4 mb-6">
          <h3 class="font-display text-2xl md:text-3xl">Vista Previa</h3>
          <button onclick="closePreview()" class="rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Cerrar">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
              <path d="M6 6l12 12M18 6 6 18" stroke="white" stroke-width="1.8" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
        
        <div id="previewContent" class="prose prose-invert max-w-none">
          <!-- Contenido de vista previa se cargará aquí -->
        </div>
      </div>
    </div>
  </div>

  <!-- Toast -->
  <div id="toast" class="fixed bottom-6 left-1/2 -translate-x-1/2 z-50 hidden">
    <div class="glass rounded-xl px-4 py-3 flex items-center gap-2 bg-softpink/25">
      <span id="toastMsg">¡Hecho!</span>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/database.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/editor.js"></script>
</body>
</html>
