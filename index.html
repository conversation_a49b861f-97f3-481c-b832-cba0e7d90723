<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Ecos del Alma — Blog de Poesía y Reflexiones</title>
  <meta name="description" content="Susurros que se vuelven palabras — un rincón acogedor para poemas, reflexiones e historias gentiles que tocan el alma." />
  <meta name="keywords" content="poesía, reflexiones, blog, escritura, alma, susurros, español" />
  <meta name="author" content="Ecos del Alma" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            display: ['"Playfair Display"', 'serif'],
            body: ['"Nunito Sans"', 'ui-sans-serif', 'system-ui', 'sans-serif']
          },
          colors: {
            lavender: {
              light: '#c4b5fd', // violet-300-ish
              DEFAULT: '#a78bfa', // violet-400/500
              deep: '#7c3aed' // violet-700
            },
            softpink: {
              light: '#fbcfe8', // pink-200
              DEFAULT: '#f472b6', // pink-400/500
              deep: '#db2777' // pink-600/700
            }
          },
          boxShadow: {
            glow: '0 10px 30px rgba(244, 114, 182, 0.25)'
          }
        }
      }
    }
  </script>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&family=Playfair+Display:wght@500;700&display=swap" rel="stylesheet"/>

  <!-- Magical CSS -->
  <link rel="stylesheet" href="css/magical.css">

  <style>
    ::selection { background: rgba(244,114,182,.35); color: #fff; }
    .scroll-smooth { scroll-behavior: smooth; }

    /* Hero specific animations */
    .hero-content > * {
      opacity: 0;
      animation: slide-in-up 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    .hero-content > *:nth-child(1) { animation-delay: 0.1s; }
    .hero-content > *:nth-child(2) { animation-delay: 0.3s; }
    .hero-content > *:nth-child(3) { animation-delay: 0.5s; }
    .hero-content > *:nth-child(4) { animation-delay: 0.7s; }

    /* Blog cards stagger animation */
    .blog-grid .blog-card:nth-child(1) { animation-delay: 0.1s; }
    .blog-grid .blog-card:nth-child(2) { animation-delay: 0.2s; }
    .blog-grid .blog-card:nth-child(3) { animation-delay: 0.3s; }
  </style>
</head>
<body class="scroll-smooth font-body text-white min-h-screen relative overflow-x-hidden">
  <!-- Magical Background -->
  <div class="magical-background"></div>

  <!-- Particles -->
  <div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
  </div>

  <!-- Header / Nav -->
  <header class="sticky top-0 z-30">
    <nav class="nav-magical mx-4 mt-4 rounded-2xl px-5 py-3 md:mx-auto md:max-w-6xl md:px-6 shadow-lg">
      <div class="flex items-center justify-between gap-4">
        <a href="#top" class="group inline-flex items-center gap-3 hover-lift">
          <!-- Feather/Quill SVG -->
          <svg width="28" height="28" viewBox="0 0 24 24" fill="none" class="text-softpink">
            <path d="M20 3C14 3 8 9 8 9L4 13l7-1s6-6 9-9Z" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M4 13s1 3 4 4 6-1 6-1" stroke="currentColor" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <span class="font-display text-xl md:text-2xl tracking-wide text-gradient">Ecos del Alma</span>
        </a>
        <div class="hidden md:flex items-center gap-3">
          <a href="#posts" class="nav-item px-3 py-2 rounded-xl hover:bg-white/10 transition">Blogs</a>
          <a href="blogs.html" class="nav-item px-3 py-2 rounded-xl hover:bg-white/10 transition">Todos los Blogs</a>
          <a href="#about" class="nav-item px-3 py-2 rounded-xl hover:bg-white/10 transition">Acerca</a>
          <a href="#contact" class="nav-item px-3 py-2 rounded-xl hover:bg-white/10 transition">Contacto</a>

        </div>
        <!-- Mobile menu simple anchor shortcuts -->
        <div class="md:hidden flex items-center gap-2">
          <a href="blogs.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Blogs</a>
          <a href="#menu" class="px-3 py-2 rounded-xl hover:bg-white/10 transition" onclick="openMobileMenu(event)">Menú</a>
        </div>
      </div>
    </nav>
    <!-- Mobile dropdown -->
    <div id="mobileMenu" class="hidden mx-4 mt-2 glass rounded-2xl px-4 py-3 md:hidden">
      <div class="flex flex-col">
        <a href="#posts" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Blogs</a>
        <a href="blogs.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Todos los Blogs</a>
        <a href="#about" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Acerca</a>
        <a href="#contact" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Contacto</a>
        <a href="#subscribe" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Suscribirse</a>
        <a href="admin.html" class="px-3 py-2 rounded-xl hover:bg-white/10 transition">Admin</a>
      </div>
    </div>
  </header>

  <!-- Hero -->
  <section id="top" class="hero-magical mt-8 md:mt-12 px-4 md:px-6">
    <div class="max-w-6xl mx-auto grid md:grid-cols-[1.2fr_.8fr] gap-6 items-stretch">
      <!-- Hero card -->
      <div class="glass-enhanced rounded-3xl p-7 md:p-10 shadow-xl card-magical">
        <div class="flex flex-col gap-4">
          <div class="inline-flex items-center gap-2 text-softpink-light">
            <span class="text-sm uppercase tracking-[0.2em]">Writer’s Blog</span>
            <span class="w-1 h-1 rounded-full bg-softpink/80"></span>
            <span class="text-sm">Poetry • Reflections</span>
          </div>
          <h1 class="font-display text-4xl md:text-5xl leading-tight">
            Ecos del Alma
          </h1>
          <p class="text-white/85 text-lg md:text-xl">
            Susurros que se vuelven palabras — un rincón acogedor para poemas, reflexiones e historias gentiles.
          </p>

          <div class="mt-4 md:mt-6 flex flex-col sm:flex-row gap-3">
            <form id="searchForm" class="flex-1" onsubmit="event.preventDefault()">
              <div class="search-magical flex items-center gap-3 glass-enhanced rounded-2xl px-4 py-3 focus-within:ring-2 focus-within:ring-softpink">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" class="opacity-80">
                  <circle cx="11" cy="11" r="7" stroke="white" stroke-width="1.5" />
                  <path d="M20 20l-3.2-3.2" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
                <input id="searchInput" type="text" placeholder="Buscar blogs, temas, palabras..." class="w-full bg-transparent outline-none placeholder-white/60"/>
                <button id="clearSearch" type="button" class="hidden text-softpink hover:text-softpink-deep transition hover-lift">Limpiar</button>
              </div>
            </form>
            <button id="randomRead" class="btn-magical inline-flex items-center justify-center gap-2 rounded-2xl px-5 py-3 transition shadow-glow hover-lift">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none"><path d="M5 12h9M12 5l7 7-7 7" stroke="white" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/></svg>
              <span>Sorpréndeme</span>
            </button>
          </div>

          <!-- Category filters -->
          <div class="mt-4 flex flex-wrap gap-2" id="filters">
            <button data-filter="all" class="filter-chip active glass-enhanced px-4 py-2 rounded-xl hover:bg-white/20 transition">Todos</button>
            <button data-filter="poesia" class="filter-chip glass-enhanced px-4 py-2 rounded-xl hover:bg-white/20 transition">Poesía</button>
            <button data-filter="reflexiones" class="filter-chip glass-enhanced px-4 py-2 rounded-xl hover:bg-white/20 transition">Reflexiones</button>
            <button data-filter="naturaleza" class="filter-chip glass-enhanced px-4 py-2 rounded-xl hover:bg-white/20 transition">Naturaleza</button>
            <button data-filter="amor" class="filter-chip glass-enhanced px-4 py-2 rounded-xl hover:bg-white/20 transition">Amor</button>
          </div>
        </div>
      </div>

      <!-- Side card: Subscribe -->
      <div id="subscribe" class="glass-enhanced rounded-3xl p-7 md:p-8 flex flex-col justify-between card-magical">
        <div>
          <h3 class="font-display text-2xl">Recibe actualizaciones gentiles</h3>
          <p class="text-white/80 mt-2">Notas ocasionales y nuevos blogs. Sin ruido, solo susurros.</p>
          <form id="subscribeForm" class="form-magical mt-5 space-y-3" onsubmit="handleSubscribe(event)">
            <div class="glass-enhanced rounded-xl px-4 py-3">
              <input type="text" name="name" placeholder="Tu nombre" class="bg-transparent outline-none w-full placeholder-white/70"/>
            </div>
            <div class="glass-enhanced rounded-xl px-4 py-3">
              <input type="email" name="email" placeholder="Tu email" class="bg-transparent outline-none w-full placeholder-white/70" required/>
            </div>
            <button type="submit" class="btn-magical w-full rounded-xl px-4 py-3 transition shadow-glow">Suscribirse</button>
            <p class="text-xs text-white/70">Los datos se guardan localmente en tu navegador.</p>
          </form>
        </div>
        <div class="mt-6">
          <div class="flex items-center gap-2 text-white/70 text-sm">
            <span class="w-2 h-2 bg-softpink rounded-full"></span>
            <span>Privado y respetuoso con tu tiempo.</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Posts -->
  <section id="posts" class="px-4 md:px-6 mt-10 md:mt-14">
    <div class="max-w-6xl mx-auto">
      <div class="flex items-end justify-between mb-4">
        <h2 class="font-display text-2xl md:text-3xl">Latest whispers</h2>
        <span id="resultsCount" class="text-white/70 text-sm"></span>
      </div>

      <div id="postsGrid" class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Post Card 1 -->
        <article class="post-card glass rounded-3xl overflow-hidden flex flex-col" data-id="p1" data-tags="poetry love" data-title="A Quiet Bloom">
          <div class="h-28 bg-gradient-to-r from-softpink/40 via-white/10 to-softpink/30"></div>
          <div class="p-6 flex-1 flex flex-col gap-3">
            <h3 class="font-display text-xl">A Quiet Bloom</h3>
            <p class="text-white/80">In the hush between thoughts, something tender unfolds...</p>
            <div class="flex flex-wrap gap-2">
              <span class="glass text-xs px-3 py-1 rounded-full">Poetry</span>
              <span class="glass text-xs px-3 py-1 rounded-full">Love</span>
            </div>
            <div class="mt-auto flex items-center justify-between pt-3">
              <button class="btn-read inline-flex items-center gap-2 rounded-xl px-4 py-2 bg-softpink hover:bg-softpink-deep transition" data-open="p1">
                <span>Read</span>
              </button>
              <button class="btn-fav group rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Save" data-id="p1">
                <svg class="heart" width="22" height="22" viewBox="0 0 24 24" fill="none">
                  <path d="M19.5 13.5 12 21l-7.5-7.5a5 5 0 1 1 7.07-7.07L12 6l.43-.57a5 5 0 1 1 7.07 7.07Z" stroke="white" stroke-width="1.4" fill="transparent"></path>
                </svg>
              </button>
            </div>
          </div>
          <!-- Hidden full content -->
          <div class="hidden post-content">
            <p>Some mornings, the silence speaks before I do. It unfolds like a petal, not rushing, not afraid, simply opening. In that quiet, I meet myself and call it love.</p>
            <p>And if the world insists on thunder, I’ll keep a room of light where whispers can still grow wild.</p>
          </div>
        </article>

        <!-- Post Card 2 -->
        <article class="post-card glass rounded-3xl overflow-hidden flex flex-col" data-id="p2" data-tags="reflections nature" data-title="Pebbles and Rivers">
          <div class="h-28 bg-gradient-to-r from-white/10 via-softpink/30 to-white/10"></div>
          <div class="p-6 flex-1 flex flex-col gap-3">
            <h3 class="font-display text-xl">Pebbles and Rivers</h3>
            <p class="text-white/80">Every small choice reshapes the current, and the shore remembers...</p>
            <div class="flex flex-wrap gap-2">
              <span class="glass text-xs px-3 py-1 rounded-full">Reflections</span>
              <span class="glass text-xs px-3 py-1 rounded-full">Nature</span>
            </div>
            <div class="mt-auto flex items-center justify-between pt-3">
              <button class="btn-read inline-flex items-center gap-2 rounded-xl px-4 py-2 bg-softpink hover:bg-softpink-deep transition" data-open="p2">Read</button>
              <button class="btn-fav rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Save" data-id="p2">
                <svg class="heart" width="22" height="22" viewBox="0 0 24 24" fill="none">
                  <path d="M19.5 13.5 12 21l-7.5-7.5a5 5 0 1 1 7.07-7.07L12 6l.43-.57a5 5 0 1 1 7.07 7.07Z" stroke="white" stroke-width="1.4" fill="transparent"></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="hidden post-content">
            <p>I carry pebbles of moments in my pocket. Each one, smooth with time, reminds me the river is patient, and so can I be.</p>
            <p>To flow is not to forget — it’s to remember in motion.</p>
          </div>
        </article>

        <!-- Post Card 3 -->
        <article class="post-card glass rounded-3xl overflow-hidden flex flex-col" data-id="p3" data-tags="poetry nature" data-title="Skyletters">
          <div class="h-28 bg-gradient-to-r from-softpink/25 via-white/10 to-softpink/25"></div>
          <div class="p-6 flex-1 flex flex-col gap-3">
            <h3 class="font-display text-xl">Skyletters</h3>
            <p class="text-white/80">Clouds keep the softest archives — our daydreams folded into light...</p>
            <div class="flex flex-wrap gap-2">
              <span class="glass text-xs px-3 py-1 rounded-full">Poetry</span>
              <span class="glass text-xs px-3 py-1 rounded-full">Nature</span>
            </div>
            <div class="mt-auto flex items-center justify-between pt-3">
              <button class="btn-read inline-flex items-center gap-2 rounded-xl px-4 py-2 bg-softpink hover:bg-softpink-deep transition" data-open="p3">Read</button>
              <button class="btn-fav rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Save" data-id="p3">
                <svg class="heart" width="22" height="22" viewBox="0 0 24 24" fill="none">
                  <path d="M19.5 13.5 12 21l-7.5-7.5a5 5 0 1 1 7.07-7.07L12 6l.43-.57a5 5 0 1 1 7.07 7.07Z" stroke="white" stroke-width="1.4" fill="transparent"></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="hidden post-content">
            <p>Some letters I never send, I give to the sky. The wind reads everything kindly.</p>
            <p>Today, my sentence was just a breath. It came back as peace.</p>
          </div>
        </article>

        <!-- Post Card 4 (initially hidden) -->
        <article class="post-card hidden-post glass rounded-3xl overflow-hidden flex flex-col hidden" data-id="p4" data-tags="reflections love" data-title="Rooms of Light">
          <div class="h-28 bg-gradient-to-r from-white/10 via-softpink/20 to-white/10"></div>
          <div class="p-6 flex-1 flex flex-col gap-3">
            <h3 class="font-display text-xl">Rooms of Light</h3>
            <p class="text-white/80">We all keep a window inside us. Some afternoons, the sun finds it...</p>
            <div class="flex flex-wrap gap-2">
              <span class="glass text-xs px-3 py-1 rounded-full">Reflections</span>
              <span class="glass text-xs px-3 py-1 rounded-full">Love</span>
            </div>
            <div class="mt-auto flex items-center justify-between pt-3">
              <button class="btn-read inline-flex items-center gap-2 rounded-xl px-4 py-2 bg-softpink hover:bg-softpink-deep transition" data-open="p4">Read</button>
              <button class="btn-fav rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Save" data-id="p4">
                <svg class="heart" width="22" height="22" viewBox="0 0 24 24" fill="none">
                  <path d="M19.5 13.5 12 21l-7.5-7.5a5 5 0 1 1 7.07-7.07L12 6l.43-.57a5 5 0 1 1 7.07 7.07Z" stroke="white" stroke-width="1.4" fill="transparent"></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="hidden post-content">
            <p>Some days, all it takes is a warm patch on the floor to remember: I am here, and light knows it.</p>
          </div>
        </article>

        <!-- Post Card 5 (hidden) -->
        <article class="post-card hidden-post glass rounded-3xl overflow-hidden flex flex-col hidden" data-id="p5" data-tags="poetry" data-title="Ink and Quiet">
          <div class="h-28 bg-gradient-to-r from-softpink/35 via-white/10 to-softpink/35"></div>
          <div class="p-6 flex-1 flex flex-col gap-3">
            <h3 class="font-display text-xl">Ink and Quiet</h3>
            <p class="text-white/80">There’s a hush my pen understands, a space it never rushes through...</p>
            <div class="flex flex-wrap gap-2">
              <span class="glass text-xs px-3 py-1 rounded-full">Poetry</span>
            </div>
            <div class="mt-auto flex items-center justify-between pt-3">
              <button class="btn-read inline-flex items-center gap-2 rounded-xl px-4 py-2 bg-softpink hover:bg-softpink-deep transition" data-open="p5">Read</button>
              <button class="btn-fav rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Save" data-id="p5">
                <svg class="heart" width="22" height="22" viewBox="0 0 24 24" fill="none">
                  <path d="M19.5 13.5 12 21l-7.5-7.5a5 5 0 1 1 7.07-7.07L12 6l.43-.57a5 5 0 1 1 7.07 7.07Z" stroke="white" stroke-width="1.4" fill="transparent"></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="hidden post-content">
            <p>Between words, a meadow. I walk it slowly, so the commas can catch their breath.</p>
          </div>
        </article>

        <!-- Post Card 6 (hidden) -->
        <article class="post-card hidden-post glass rounded-3xl overflow-hidden flex flex-col hidden" data-id="p6" data-tags="reflections nature" data-title="Dawn Lessons">
          <div class="h-28 bg-gradient-to-r from-white/10 via-softpink/25 to-white/10"></div>
          <div class="p-6 flex-1 flex flex-col gap-3">
            <h3 class="font-display text-xl">Dawn Lessons</h3>
            <p class="text-white/80">Morning teaches softly — first the outline, then the world...</p>
            <div class="flex flex-wrap gap-2">
              <span class="glass text-xs px-3 py-1 rounded-full">Reflections</span>
              <span class="glass text-xs px-3 py-1 rounded-full">Nature</span>
            </div>
            <div class="mt-auto flex items-center justify-between pt-3">
              <button class="btn-read inline-flex items-center gap-2 rounded-xl px-4 py-2 bg-softpink hover:bg-softpink-deep transition" data-open="p6">Read</button>
              <button class="btn-fav rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Save" data-id="p6">
                <svg class="heart" width="22" height="22" viewBox="0 0 24 24" fill="none">
                  <path d="M19.5 13.5 12 21l-7.5-7.5a5 5 0 1 1 7.07-7.07L12 6l.43-.57a5 5 0 1 1 7.07 7.07Z" stroke="white" stroke-width="1.4" fill="transparent"></path>
                </svg>
              </button>
            </div>
          </div>
          <div class="hidden post-content">
            <p>At first light, even my worries yawn. The day is new, and that is enough of a miracle to begin again.</p>
          </div>
        </article>
      </div>

      <div class="flex justify-center mt-8">
        <button id="loadMore" class="rounded-xl px-6 py-3 glass hover:bg-white/20 transition">Load more</button>
      </div>
    </div>
  </section>

  <!-- About -->
  <section id="about" class="px-4 md:px-6 mt-14">
    <div class="max-w-6xl mx-auto grid lg:grid-cols-2 gap-6 items-stretch">
      <div class="glass rounded-3xl p-8">
        <h3 class="font-display text-2xl">About the writer</h3>
        <p class="text-white/85 mt-3">
          I write to trace the quiet places we carry — the tender edges, the soft middles, the little rooms of light.
          Ecos del Alma is where those notes gather. Thank you for reading.
        </p>
        <div class="mt-6 flex items-center gap-2 text-white/70">
          <span class="w-2 h-2 rounded-full bg-softpink"></span>
          <span>Based wherever the sky is kind today.</span>
        </div>
      </div>
      <div class="glass rounded-3xl p-8">
        <h3 class="font-display text-2xl">Writing schedule</h3>
        <ul class="mt-3 space-y-2 text-white/85">
          <li>• New poem every other week</li>
          <li>• Reflections monthly</li>
          <li>• Occasional short stories when the moon insists</li>
        </ul>
        <div class="mt-6">
          <a href="#subscribe" class="inline-flex items-center gap-2 rounded-xl px-5 py-3 bg-softpink hover:bg-softpink-deep transition shadow-glow">Get updates</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact -->
  <section id="contact" class="px-4 md:px-6 mt-14 mb-16">
    <div class="max-w-6xl mx-auto grid lg:grid-cols-2 gap-6">
      <div class="glass rounded-3xl p-8">
        <h3 class="font-display text-2xl">Say hello</h3>
        <p class="text-white/85 mt-2">Notes, ideas, and kind words are always welcome.</p>
        <form id="contactForm" class="mt-5 space-y-3" onsubmit="handleContact(event)">
          <div class="glass rounded-xl px-4 py-3">
            <input name="name" placeholder="Your name" class="bg-transparent outline-none w-full placeholder-white/70" required/>
          </div>
          <div class="glass rounded-xl px-4 py-3">
            <input type="email" name="email" placeholder="Your email" class="bg-transparent outline-none w-full placeholder-white/70" required/>
          </div>
          <div class="glass rounded-xl px-4 py-3">
            <textarea name="message" rows="4" placeholder="Your message" class="bg-transparent outline-none w-full placeholder-white/70" required></textarea>
          </div>
          <button type="submit" class="rounded-xl px-5 py-3 bg-softpink hover:bg-softpink-deep transition shadow-glow">Send</button>
          <p class="text-xs text-white/70">Demo only — messages are shown on-screen, not sent.</p>
        </form>
      </div>
      <div class="glass rounded-3xl p-8 flex items-center justify-center">
        <div class="text-center">
          <div class="mx-auto w-24 h-24 rounded-full bg-softpink/30 flex items-center justify-center mb-4">
            <svg width="36" height="36" viewBox="0 0 24 24" fill="none">
              <path d="M4 7l8 5 8-5" stroke="white" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
              <rect x="4" y="5" width="16" height="14" rx="2" stroke="white" stroke-width="1.6" />
            </svg>
          </div>
          <p class="text-white/85">Prefer email? Use the form — I’ll see it here in a friendly preview.</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="px-4 md:px-6 pb-10">
    <div class="max-w-6xl mx-auto glass rounded-3xl p-6 flex flex-col sm:flex-row items-center justify-between">
      <p class="text-white/80">&copy; <span id="year"></span> Ecos del Alma</p>
      <div class="flex items-center gap-2 text-white/70 text-sm mt-2 sm:mt-0">
        <span>Made with calm and curiosity.</span>
      </div>
    </div>
  </footer>

  <!-- Post Modal -->
  <div id="postModal" class="fixed inset-0 z-40 hidden">
    <div class="absolute inset-0 bg-black/50"></div>
    <div class="relative max-w-3xl mx-auto mt-16 md:mt-20 px-4">
      <div class="glass rounded-3xl p-6 md:p-8">
        <div class="flex items-start justify-between gap-4">
          <h3 id="modalTitle" class="font-display text-2xl"></h3>
          <button onclick="closeModal()" class="rounded-xl px-3 py-2 hover:bg-white/10 transition" aria-label="Close">
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none"><path d="M6 6l12 12M18 6 6 18" stroke="white" stroke-width="1.8" stroke-linecap="round"/></svg>
          </button>
        </div>
        <div id="modalBody" class="prose prose-invert mt-4 max-w-none">
          <!-- Content injected -->
        </div>
        <div class="mt-6 flex items-center justify-between">
          <button id="modalPrev" class="rounded-xl px-4 py-2 glass hover:bg-white/20 transition">Previous</button>
          <button id="modalNext" class="rounded-xl px-4 py-2 glass hover:bg-white/20 transition">Next</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast -->
  <div id="toast" class="fixed bottom-6 left-1/2 -translate-x-1/2 z-50 hidden">
    <div class="glass rounded-xl px-4 py-3 flex items-center gap-2 bg-softpink/25">
      <span id="toastMsg">Done!</span>
    </div>
  </div>

  <!-- Back to top -->
  <button id="toTop" class="hidden fixed bottom-6 right-6 z-40 rounded-full p-3 glass hover:bg-white/20 transition" aria-label="Back to top">
    <svg width="22" height="22" viewBox="0 0 24 24" fill="none"><path d="M12 19V5m0 0-7 7m7-7 7 7" stroke="white" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/></svg>
  </button>

  <script>
    // Utilities
    const $ = (sel, root=document) => root.querySelector(sel);
    const $$ = (sel, root=document) => Array.from(root.querySelectorAll(sel));
    const toast = (msg) => {
      const el = $('#toast');
      $('#toastMsg').textContent = msg;
      el.classList.remove('hidden');
      clearTimeout(window.__toastTimer);
      window.__toastTimer = setTimeout(()=> el.classList.add('hidden'), 2000);
    };

    // Header mobile menu
    function openMobileMenu(e){ e.preventDefault(); $('#mobileMenu').classList.toggle('hidden'); }

    // Year
    $('#year').textContent = new Date().getFullYear();

    // Posts filtering and search
    const posts = $$('.post-card');
    const resultsCount = $('#resultsCount');
    const searchInput = $('#searchInput');
    const clearSearch = $('#clearSearch');
    const filters = $$('#filters .filter-chip');
    let currentFilter = 'all';

    function normalize(t){ return (t || '').toLowerCase().trim(); }

    function applyFilters(){
      const q = normalize(searchInput.value);
      let visible = 0;
      posts.forEach(card => {
        const tags = card.dataset.tags || '';
        const title = card.dataset.title || '';
        const text = (title + ' ' + tags).toLowerCase();
        const matchesText = !q || text.includes(q);
        const matchesFilter = currentFilter === 'all' || tags.split(' ').includes(currentFilter);
        const shouldShow = matchesText && matchesFilter;
        if(shouldShow){
          card.classList.remove('hidden');
          if(card.classList.contains('hidden-post')) {
            // keep hidden posts hidden until "Load more" pressed
            if($('#loadMore').dataset.opened !== 'true') {
              card.classList.add('hidden');
            }
          }
          if(!card.classList.contains('hidden')) visible++;
        } else {
          card.classList.add('hidden');
        }
      });
      const totalVisible = visible;
      resultsCount.textContent = totalVisible === posts.length ? '' : totalVisible + ' shown';
      clearSearch.classList.toggle('hidden', !q);
    }

    searchInput.addEventListener('input', applyFilters);
    clearSearch.addEventListener('click', ()=>{ searchInput.value=''; applyFilters(); });

    filters.forEach(btn=>{
      btn.addEventListener('click', ()=>{
        filters.forEach(b=>b.classList.remove('active'));
        btn.classList.add('active');
        currentFilter = btn.dataset.filter;
        applyFilters();
      });
    });

    // Load more
    $('#loadMore').addEventListener('click', (e)=>{
      e.preventDefault();
      $$('.hidden-post').forEach(el => el.classList.remove('hidden'));
      e.currentTarget.dataset.opened = 'true';
      e.currentTarget.classList.add('hidden');
      applyFilters();
    });

    // Favorites (localStorage)
    const favKey = 'ecos_favs';
    function getFavs(){ try { return JSON.parse(localStorage.getItem(favKey)) || []; } catch { return []; } }
    function setFavs(list){ localStorage.setItem(favKey, JSON.stringify(list)); }
    function updateFavButton(btn, isFav){
      const path = btn.querySelector('.heart path');
      if(isFav){
        path.setAttribute('fill', 'rgba(244,114,182,.9)');
        path.setAttribute('stroke', 'rgba(244,114,182,1)');
      } else {
        path.setAttribute('fill', 'transparent');
        path.setAttribute('stroke', 'white');
      }
    }
    function initFavs(){
      const favs = getFavs();
      $$('.btn-fav').forEach(btn=>{
        const id = btn.dataset.id;
        updateFavButton(btn, favs.includes(id));
        btn.addEventListener('click', ()=>{
          const favsNow = getFavs();
          const idx = favsNow.indexOf(id);
          if(idx>-1){ favsNow.splice(idx,1); toast('Removed from saved'); }
          else { favsNow.push(id); toast('Saved'); }
          setFavs(favsNow);
          updateFavButton(btn, favsNow.includes(id));
        });
      });
    }
    initFavs();

    // Modal reading
    const modal = $('#postModal');
    const modalTitle = $('#modalTitle');
    const modalBody = $('#modalBody');
    const modalPrev = $('#modalPrev');
    const modalNext = $('#modalNext');
    let order = posts.map(c=>c.dataset.id);
    let currentIndex = -1;
    function openModalById(id){
      const node = posts.find(p=>p.dataset.id===id);
      if(!node) return;
      modalTitle.textContent = node.dataset.title;
      const content = $('.post-content', node)?.innerHTML || '<p>No content.</p>';
      modalBody.innerHTML = content;
      currentIndex = order.indexOf(id);
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
    function closeModal(){
      modal.classList.add('hidden');
      document.body.style.overflow = '';
    }
    window.closeModal = closeModal;

    $$('.btn-read').forEach(btn=>{
      btn.addEventListener('click', ()=>{
        openModalById(btn.dataset.open);
      });
    });
    modalPrev.addEventListener('click', ()=>{
      if(currentIndex<=0) currentIndex = order.length;
      currentIndex--;
      openModalById(order[currentIndex]);
    });
    modalNext.addEventListener('click', ()=>{
      currentIndex = (currentIndex+1) % order.length;
      openModalById(order[currentIndex]);
    });
    modal.addEventListener('click', (e)=>{ if(e.target===modal) closeModal(); });

    // Surprise me
    $('#randomRead').addEventListener('click', ()=>{
      const visiblePosts = posts.filter(p=>!p.classList.contains('hidden'));
      const list = visiblePosts.length ? visiblePosts : posts;
      const pick = list[Math.floor(Math.random()*list.length)];
      openModalById(pick.dataset.id);
    });

    // Subscribe / Contact handlers
    function handleSubscribe(e){
      e.preventDefault();
      const form = e.currentTarget;
      const name = form.name.value.trim() || 'Friend';
      toast(`Welcome, ${name}!`);
      form.reset();
      clearSearch.classList.add('hidden');
    }
    window.handleSubscribe = handleSubscribe;

    function handleContact(e){
      e.preventDefault();
      const form = e.currentTarget;
      const name = form.name.value.trim() || 'Friend';
      const email = form.email.value.trim();
      const message = form.message.value.trim();
      if(!email || !message){ toast('Please fill all fields'); return; }
      toast('Preview shown below');
      // Show a lightweight preview inside the form area
      const preview = document.createElement('div');
      preview.className = 'mt-3 glass rounded-xl p-4 text-white/90';
      preview.innerHTML = `<div class="text-sm">From: <strong>${name}</strong> &lt;${email}&gt;</div><div class="mt-2 whitespace-pre-wrap">${message}</div>`;
      form.after(preview);
      form.reset();
    }
    window.handleContact = handleContact;

    // Back to top
    const toTop = $('#toTop');
    window.addEventListener('scroll', ()=>{
      const show = window.scrollY > 500;
      toTop.classList.toggle('hidden', !show);
    });
    toTop.addEventListener('click', ()=>{
      window.scrollTo({ top: 0, behavior: 'smooth' });
    });

    // Initialize counts and filters
    applyFilters();
  </script>

  <!-- External Scripts -->
  <script src="js/database.js"></script>
  <script src="js/utils.js"></script>
  <script src="js/home.js"></script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'982087bce6a4a04c',t:'MTc1ODM2Mjk5Ny4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
