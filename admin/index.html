<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Acceso Administrativo — Ecos del Alma</title>
  <meta name="description" content="Panel de administración para Ecos del Alma" />
  <meta name="robots" content="noindex, nofollow" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          fontFamily: {
            display: ['"Playfair Display"', 'serif'],
            body: ['"Nunito Sans"', 'ui-sans-serif', 'system-ui', 'sans-serif']
          },
          colors: {
            lavender: {
              light: '#c4b5fd',
              DEFAULT: '#a78bfa',
              deep: '#7c3aed'
            },
            softpink: {
              light: '#fbcfe8',
              DEFAULT: '#f472b6',
              deep: '#db2777'
            }
          }
        }
      }
    }
  </script>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com"/>
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin/>
  <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&family=Playfair+Display:wght@500;700&display=swap" rel="stylesheet"/>

  <!-- Magical CSS -->
  <link rel="stylesheet" href="../css/magical.css">

  <style>
    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .login-card {
      backdrop-filter: blur(25px);
      -webkit-backdrop-filter: blur(25px);
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2);
    }

    .password-input {
      position: relative;
    }

    .password-toggle {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      cursor: pointer;
      color: rgba(255, 255, 255, 0.7);
      transition: color 0.3s;
    }

    .password-toggle:hover {
      color: rgba(255, 255, 255, 1);
    }

    .floating-shapes {
      position: absolute;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: -1;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    .shape:nth-child(1) {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      width: 120px;
      height: 120px;
      top: 60%;
      right: 10%;
      animation-delay: 2s;
    }

    .shape:nth-child(3) {
      width: 60px;
      height: 60px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }

    .shape:nth-child(4) {
      width: 100px;
      height: 100px;
      top: 40%;
      right: 30%;
      animation-delay: 1s;
    }
  </style>
</head>
<body class="font-body text-white overflow-hidden">
  <!-- Magical Background -->
  <div class="magical-background"></div>
  
  <!-- Particles -->
  <div class="particles">
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
    <div class="particle"></div>
  </div>

  <!-- Floating Shapes -->
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>

  <!-- Login Container -->
  <div class="login-container px-4">
    <div class="login-card rounded-3xl p-8 md:p-12 w-full max-w-md mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="mb-4">
          <svg width="60" height="60" viewBox="0 0 24 24" fill="none" class="mx-auto text-softpink">
            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <h1 class="font-display text-3xl md:text-4xl mb-2 text-gradient">Ecos del Alma</h1>
        <p class="text-white/70">Panel de Administración</p>
      </div>

      <!-- Login Form -->
      <form id="loginForm" onsubmit="handleLogin(event)" class="space-y-6">
        <div>
          <label for="password" class="block text-sm font-medium mb-2 text-white/90">
            Contraseña de Acceso
          </label>
          <div class="password-input">
            <input 
              type="password" 
              id="password" 
              name="password" 
              required
              autocomplete="current-password"
              class="w-full glass-enhanced rounded-xl px-4 py-3 bg-transparent outline-none focus:ring-2 focus:ring-softpink text-white placeholder-white/50 pr-12"
              placeholder="Ingresa tu contraseña..."
            />
            <button 
              type="button" 
              class="password-toggle"
              onclick="togglePasswordVisibility()"
              aria-label="Mostrar/ocultar contraseña"
            >
              <svg id="eyeIcon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="1.5"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="hidden bg-red-500/20 border border-red-500/30 rounded-xl px-4 py-3 text-red-200 text-sm">
          <!-- Error message will be inserted here -->
        </div>

        <!-- Submit Button -->
        <button 
          type="submit" 
          class="w-full btn-magical rounded-xl px-6 py-3 font-semibold text-white transition-all duration-300"
        >
          Iniciar Sesión
        </button>
      </form>

      <!-- Footer -->
      <div class="mt-8 text-center">
        <p class="text-white/50 text-sm">
          Acceso restringido solo para administradores
        </p>
        <div class="mt-4">
          <a href="../index.html" class="text-softpink hover:text-softpink-light transition-colors text-sm">
            ← Volver al sitio principal
          </a>
        </div>
      </div>

      <!-- Security Info -->
      <div class="mt-6 p-4 glass-enhanced rounded-xl">
        <div class="flex items-start gap-3">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" class="text-softpink mt-0.5 flex-shrink-0">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="1.5"/>
            <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
          </svg>
          <div class="text-xs text-white/60">
            <p class="font-medium mb-1">Información de Seguridad:</p>
            <ul class="space-y-1">
              <li>• Las sesiones expiran automáticamente</li>
              <li>• Contraseña por defecto: <code class="bg-white/10 px-1 rounded">EcosDelAlma2024!</code></li>
              <li>• Cambia tu contraseña desde configuración</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Scripts -->
  <script src="../js/auth.js"></script>
  
  <script>
    // Toggle password visibility
    function togglePasswordVisibility() {
      const passwordInput = document.getElementById('password');
      const eyeIcon = document.getElementById('eyeIcon');
      
      if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.innerHTML = `
          <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path d="M1 1l22 22" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        `;
      } else {
        passwordInput.type = 'password';
        eyeIcon.innerHTML = `
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="1.5"/>
          <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="1.5"/>
        `;
      }
    }

    // Auto-focus password field
    document.addEventListener('DOMContentLoaded', () => {
      document.getElementById('password').focus();
    });

    // Handle Enter key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        const form = document.getElementById('loginForm');
        if (form) {
          form.dispatchEvent(new Event('submit'));
        }
      }
    });

    // Check if already authenticated
    document.addEventListener('DOMContentLoaded', () => {
      if (window.AuthSystem && window.AuthSystem.isAuthenticated) {
        window.location.href = '../admin.html';
      }
    });
  </script>
</body>
</html>
