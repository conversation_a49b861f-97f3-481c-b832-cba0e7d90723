/* Ecos del Alma - Magical CSS Animations & Effects */

:root {
  --lavender-light: #c4b5fd;
  --lavender: #a78bfa;
  --lavender-deep: #7c3aed;
  --softpink-light: #fbcfe8;
  --softpink: #f472b6;
  --softpink-deep: #db2777;
  --white: #ffffff;
  --black: #000000;
}

/* Global Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(244, 114, 182, 0.3); }
  50% { box-shadow: 0 0 40px rgba(244, 114, 182, 0.6); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced Background */
.magical-background {
  position: fixed;
  inset: 0;
  z-index: -20;
  background: linear-gradient(135deg, 
    var(--lavender-light) 0%, 
    var(--lavender) 25%, 
    var(--lavender-deep) 50%, 
    var(--softpink) 75%, 
    var(--softpink-deep) 100%);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

.magical-background::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 20% 80%, rgba(244, 114, 182, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(167, 139, 250, 0.3) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: float 8s ease-in-out infinite;
}

/* Floating Particles */
.particles {
  position: fixed;
  inset: 0;
  z-index: -10;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: sparkle 3s ease-in-out infinite;
}

.particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { top: 60%; left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { top: 40%; left: 80%; animation-delay: 2s; }
.particle:nth-child(4) { top: 80%; left: 70%; animation-delay: 0.5s; }
.particle:nth-child(5) { top: 30%; left: 50%; animation-delay: 1.5s; }

/* Enhanced Glass Effect */
.glass-enhanced {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-enhanced:hover {
  background: rgba(255, 255, 255, 0.18);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Magical Buttons */
.btn-magical {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--softpink), var(--softpink-deep));
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.btn-magical::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.btn-magical:hover::before {
  left: 100%;
}

.btn-magical:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(244, 114, 182, 0.4);
}

.btn-magical:active {
  transform: translateY(0);
}

/* Secondary Button */
.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Card Animations */
.card-magical {
  animation: slide-in-up 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-magical:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(244, 114, 182, 0.3);
}

/* Navigation Enhancements */
.nav-magical {
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  animation: slide-in-up 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--softpink), var(--lavender));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.nav-item:hover::after {
  width: 100%;
}

/* Hero Section */
.hero-magical {
  animation: fade-in 1s cubic-bezier(0.4, 0, 0.2, 1);
}

.hero-title {
  background: linear-gradient(135deg, var(--white), var(--softpink-light), var(--lavender-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: slide-in-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.hero-subtitle {
  animation: slide-in-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.hero-description {
  animation: slide-in-left 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
}

/* Search Bar */
.search-magical {
  position: relative;
  overflow: hidden;
}

.search-magical::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, var(--softpink), var(--lavender));
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: inherit;
}

.search-magical:focus-within::before {
  opacity: 0.1;
}

.search-magical input {
  position: relative;
  z-index: 1;
}

/* Filter Chips */
.filter-chip {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.filter-chip::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, var(--softpink) 0%, var(--lavender) 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translate(-50%, -50%);
  border-radius: 50%;
  opacity: 0.2;
}

.filter-chip:hover::before,
.filter-chip.active::before {
  width: 100%;
  height: 100%;
}

/* Blog Cards */
.blog-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: scale-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.blog-card:hover {
  transform: translateY(-12px) rotateX(5deg);
  box-shadow: 0 25px 80px rgba(244, 114, 182, 0.4);
}

.blog-card .blog-gradient {
  background: linear-gradient(135deg, 
    var(--softpink-light) 0%, 
    var(--lavender-light) 50%, 
    var(--softpink) 100%);
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* Loading Animation */
.loading-magical {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--softpink);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Modal Enhancements */
.modal-magical {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.modal-content {
  animation: scale-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Form Enhancements */
.form-magical input,
.form-magical textarea,
.form-magical select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.form-magical input:focus,
.form-magical textarea:focus,
.form-magical select:focus {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(244, 114, 182, 0.2);
}

/* Responsive Animations */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Utility Classes */
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

.hover-lift:hover {
  transform: translateY(-4px);
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(244, 114, 182, 0.5);
}

.text-gradient {
  background: linear-gradient(135deg, var(--softpink), var(--lavender));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
